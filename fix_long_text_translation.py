#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门处理超长文本翻译的脚本
"""

import json
import os
import re
import logging
import requests
import time
from typing import Dict, Any, Optional

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LongTextTranslator:
    """处理超长文本翻译的类"""
    
    def __init__(self, model_name: str, base_url: str):
        self.model_name = model_name
        self.base_url = base_url
        self.api_url = f"{base_url}/v1/chat/completions"
        self.session = requests.Session()
    
    def split_long_text(self, text: str, max_chunk_size: int = 8000) -> list:
        """将长文本分割成较小的块"""
        # 按句子分割，保持数学公式完整
        sentences = re.split(r'(?<=[.!?])\s+', text)
        
        chunks = []
        current_chunk = ""
        
        for sentence in sentences:
            # 如果当前句子加上当前块超过限制，保存当前块并开始新块
            if len(current_chunk) + len(sentence) > max_chunk_size and current_chunk:
                chunks.append(current_chunk.strip())
                current_chunk = sentence
            else:
                current_chunk += " " + sentence if current_chunk else sentence
        
        # 添加最后一块
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def translate_chunk(self, chunk: str, chunk_index: int, total_chunks: int) -> Optional[str]:
        """翻译单个文本块"""
        prompt = f"""请将以下英文数学内容翻译成中文，保持数学公式、符号和格式不变。这是第{chunk_index+1}部分，共{total_chunks}部分。

要求：
1. 保持所有数学公式和LaTeX符号不变
2. 保持<think>和</think>标签不变
3. 翻译数学推理过程和解释文字
4. 保持原文的逻辑结构和段落格式
5. 只返回翻译后的内容，不要添加任何说明

原文：
{chunk}"""

        payload = {
            "model": self.model_name,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.1,
            "max_tokens": 12288,  # 为分块翻译设置合适的token限制
            "stream": False,
            "extra_body": {
                "chat_template_kwargs": {
                    "enable_thinking": False
                }
            }
        }
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = self.session.post(
                    self.api_url,
                    json=payload,
                    timeout=(30, 120)
                )
                response.raise_for_status()
                
                result = response.json()
                translated_text = result['choices'][0]['message']['content'].strip()
                
                if translated_text:
                    logger.info(f"✅ 成功翻译块 {chunk_index+1}/{total_chunks}")
                    return translated_text
                else:
                    logger.warning(f"块 {chunk_index+1} 翻译结果为空 (尝试 {attempt + 1})")
            
            except Exception as e:
                logger.error(f"翻译块 {chunk_index+1} 错误 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
        
        logger.error(f"❌ 块 {chunk_index+1} 翻译失败")
        return None
    
    def translate_long_text(self, text: str) -> Optional[str]:
        """翻译超长文本"""
        logger.info(f"开始分块翻译，原文长度: {len(text)} 字符")
        
        # 分割文本
        chunks = self.split_long_text(text)
        logger.info(f"文本分割为 {len(chunks)} 块")
        
        # 翻译每个块
        translated_chunks = []
        for i, chunk in enumerate(chunks):
            logger.info(f"翻译块 {i+1}/{len(chunks)}，长度: {len(chunk)} 字符")
            
            translated_chunk = self.translate_chunk(chunk, i, len(chunks))
            if translated_chunk is None:
                logger.error(f"块 {i+1} 翻译失败，停止处理")
                return None
            
            translated_chunks.append(translated_chunk)
            
            # 短暂休息避免过载
            time.sleep(1)
        
        # 合并翻译结果
        result = " ".join(translated_chunks)
        logger.info(f"翻译完成，结果长度: {len(result)} 字符")
        
        return result
    
    def translate_field(self, text: str, field_name: str) -> Optional[str]:
        """翻译单个字段"""
        if len(text) > 10000:  # 如果文本很长，使用分块翻译
            logger.info(f"字段 {field_name} 文本较长，使用分块翻译")
            return self.translate_long_text(text)
        else:
            # 对于较短的文本，使用常规翻译
            prompt = f"""请将以下英文内容翻译成中文，保持数学公式、符号和格式不变。

要求：
1. 保持所有数学公式和LaTeX符号不变
2. 保持<think>和</think>标签不变
3. 只返回翻译后的内容，不要添加任何说明

原文：
{text}"""

            payload = {
                "model": self.model_name,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.1,
                "max_tokens": 8192,
                "stream": False,
                "extra_body": {
                    "chat_template_kwargs": {
                        "enable_thinking": False
                    }
                }
            }
            
            try:
                response = self.session.post(
                    self.api_url,
                    json=payload,
                    timeout=(30, 120)
                )
                response.raise_for_status()
                
                result = response.json()
                translated_text = result['choices'][0]['message']['content'].strip()
                
                if translated_text:
                    return translated_text
                else:
                    logger.warning(f"字段 {field_name} 翻译结果为空")
                    return None
            
            except Exception as e:
                logger.error(f"翻译字段 {field_name} 错误: {e}")
                return None

def fix_chunk_003():
    """修复test_chunk_003.json文件"""
    # 配置
    model_name = "/home/<USER>/Model/LLM/Qwen/Qwen2.5-7B-Instruct-AWQ"
    base_url = "http://localhost:8666"
    
    # 文件路径
    target_file = "test_json_chunks/test_chunk_003.json"
    backup_file = "test_json_chunks_backup/test_chunk_003.json"
    
    logger.info("开始修复 test_chunk_003.json")
    logger.info("=" * 50)
    
    # 初始化翻译器
    translator = LongTextTranslator(model_name, base_url)
    
    try:
        # 读取备份文件
        if os.path.exists(backup_file):
            logger.info("从备份文件读取原始数据")
            with open(backup_file, 'r', encoding='utf-8') as f:
                original_data = json.load(f)
        else:
            logger.error("未找到备份文件")
            return False
        
        # 翻译各个字段
        translated_data = original_data.copy()
        
        # 翻译 expected_answer
        logger.info("翻译 expected_answer 字段")
        expected_answer = original_data.get('expected_answer', '')
        if expected_answer:
            translated_expected_answer = translator.translate_field(expected_answer, 'expected_answer')
            if translated_expected_answer:
                translated_data['expected_answer'] = translated_expected_answer
            else:
                logger.error("expected_answer 翻译失败")
                return False
        
        # 翻译 problem
        logger.info("翻译 problem 字段")
        problem = original_data.get('problem', '')
        if problem:
            translated_problem = translator.translate_field(problem, 'problem')
            if translated_problem:
                translated_data['problem'] = translated_problem
            else:
                logger.error("problem 翻译失败")
                return False
        
        # 翻译 generated_solution (这是最长的字段)
        logger.info("翻译 generated_solution 字段")
        generated_solution = original_data.get('generated_solution', '')
        if generated_solution:
            translated_solution = translator.translate_field(generated_solution, 'generated_solution')
            if translated_solution:
                translated_data['generated_solution'] = translated_solution
                
                # 验证<think>标签是否完整
                think_start_count = translated_solution.count('<think>')
                think_end_count = translated_solution.count('</think>')
                
                logger.info(f"翻译后 <think> 标签数量: {think_start_count}")
                logger.info(f"翻译后 </think> 标签数量: {think_end_count}")
                
                if think_start_count > 0 and think_end_count >= think_start_count:
                    logger.info("✅ <think> 标签完整")
                else:
                    logger.warning("⚠️ <think> 标签可能不完整")
            else:
                logger.error("generated_solution 翻译失败")
                return False
        
        # 保存翻译结果
        logger.info("保存翻译结果")
        with open(target_file, 'w', encoding='utf-8') as f:
            json.dump(translated_data, f, ensure_ascii=False, indent=2)
        
        logger.info("✅ test_chunk_003.json 修复完成")
        return True
        
    except Exception as e:
        logger.error(f"修复过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = fix_chunk_003()
    if success:
        print("修复成功！")
    else:
        print("修复失败！")
