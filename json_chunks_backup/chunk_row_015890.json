{"row_number": 15890, "expected_answer": "\\( f(x) = cx \\)", "problem": "Find all functions \\( f: \\mathbb{R} \\to \\mathbb{R} \\) such that:\n(i) \\( f(x + y) = f(x) + f(y) \\) for all real numbers \\( x \\) and \\( y \\).\n(ii) \\( f\\left(\\frac{1}{x}\\right) = \\frac{f(x)}{x^2} \\) for all \\( x \\neq 0 \\).", "generated_solution": "<think>\nOkay, let's try to solve this problem. We need to find all functions f from the real numbers to the real numbers that satisfy two conditions: the Cauchy functional equation f(x + y) = f(x) + f(y) for all real x and y, and another condition f(1/x) = f(x)/x² for all x not equal to zero.\n\nFirst, I remember that the Cauchy functional equation usually leads to linear functions of the form f(x) = kx, provided some regularity conditions are met, like continuity, monotonicity, or boundedness. But the problem here doesn't mention any such conditions, so there might be pathological solutions as well. But maybe the second condition restricts it to just the linear functions.\n\nLet me start by considering the Cauchy equation. Without additional constraints, the solutions are all linear functions if we assume they're linear, but in general, they can be any additive function, which might be non-linear if we don't have restrictions. However, the second condition might force the function to be linear.\n\nLet me check. Suppose f is additive, so f(x + y) = f(x) + f(y). Let's assume f is linear first. If f(x) = kx, then let's check the second condition. Plugging into condition (ii):\n\nf(1/x) = k*(1/x) and the right-hand side is f(x)/x² = (kx)/x² = k/x. So both sides are equal. So f(x) = kx satisfies both conditions. So linear functions are solutions.\n\nBut are there other solutions? The problem says \"find all functions\", so I need to check if there are any non-linear additive functions that also satisfy the second condition.\n\nAn additive function that's not linear is a function constructed using a Hamel basis, which is usually not continuous. These functions are pathological and their behavior is very wild. But maybe the second condition here can rule them out.\n\nLet me assume that f is additive and satisfies f(1/x) = f(x)/x². Let's try to explore what this implies.\n\nFirst, let's set x = 1 in condition (ii). Then f(1) = f(1)/1², which gives f(1) = f(1). So no new information there. How about another value, say x = 2. Then f(1/2) = f(2)/(2²) = f(2)/4. But if f is additive, f(2) = 2f(1), so f(1/2) = (2f(1))/4 = f(1)/2. Similarly, for integer n, f(1/n) = f(n)/n². But if f(n) = n f(1), then f(1/n) = n f(1)/n² = f(1)/n. That works because f(1/n) would be additive over the reciprocals? Wait, but how do we connect that?\n\nAlternatively, let's think of a general x. For any x ≠ 0, f(1/x) = f(x)/x². Let's substitute x with 1/x in condition (ii). Then for x ≠ 0, f(x) = f(1/x)/( (1/x)^2 ) = f(1/x) * x². But from condition (ii), we have f(1/x) = f(x)/x². Substituting back, f(x) = (f(x)/x²) * x² = f(x). So that's a tautology. Doesn't give new information.\n\nHmm. Maybe we can find a relation for f(x) in terms of x. Let's suppose f is additive, so f(q) = q f(1) for any rational number q. But for irrational numbers, additive functions can behave differently. But maybe the second condition restricts that.\n\nLet me test with x = 1. Then f(1) = f(1)/1², which as before is just an identity. Suppose f(1) = k, then for integers, f(n) = nk. For rationals, f(p/q) = (p/q)k. So on rationals, f is linear. But for irrationals, without continuity, additive functions can be non-linear. However, the second condition might impose something else.\n\nSuppose we take an irrational number, say α, and consider f(α). Let's see if we can relate f(α) to f(1/α).\n\nFrom condition (ii): f(1/α) = f(α)/α².\n\nBut f is additive, so f(1/α) should relate to f(1) over α, if we think in terms of linearity. For example, if f is linear, then f(1/α) = k*(1/α), and f(α)/α² = (k α)/α² = k / α. So that works. But in a non-linear additive function, f(α) might not be k α, so let's see.\n\nSuppose f(α) is not equal to k α. Then f(1/α) = f(α)/α². But if we apply the additive function to 1/α, maybe we can relate it to other terms. But it's unclear.\n\nAlternatively, maybe we can use the additive property to express f(1) in terms of other values. Let's consider that f(1) = k. Then, for any integer n, f(n) = nk. For 1/n, f(1/n) = f(n)/n² = (nk)/n² = k/n, which is consistent with additivity because if you add 1/n n times, you get 1, and f(1/n) + ... + f(1/n) (n times) = n*(k/n) = k = f(1). So that's okay.\n\nSo for rational numbers, f is linear. But how about irrationals? Let's suppose x is irrational. Then, how can we relate f(x) to f(1/x)?\n\nFrom condition (ii), f(1/x) = f(x)/x². But if we can write x as a sum of terms, maybe use additive properties. But it's tricky because x is irrational. Let's think.\n\nSuppose we have an additive function f. Then, for any real number x, f(x) = kx if it's linear, but if not, it's some other function. Let's assume that f is linear, then it's okay. But if f is non-linear, then there's some x where f(x) ≠ kx. Let's take such an x.\n\nSuppose there's an x where f(x) ≠ kx. Let's see if that can satisfy condition (ii).\n\nWe have f(1/x) = f(x)/x². Let's compute f(x) + f(1/x) = f(x + 1/x). Wait, because of additivity. But f(x + 1/x) = f(x) + f(1/x). But f(1/x) = f(x)/x², so f(x + 1/x) = f(x) + f(x)/x² = f(x)(1 + 1/x²).\n\nBut also, if we apply f to x + 1/x, which is some real number, perhaps. But without knowing anything about x or f(x), this doesn't help. Maybe we need a different approach.\n\nLet me consider the case when x = 1. Then f(1) = f(1)/1, which is okay. If x = -1, then f(-1) = -f(1) by additivity. Then f(-1) = -f(1). Then applying condition (ii) to x = -1: f(-1) = f(-1)/(-1)^2 = f(-1). So that's also an identity.\n\nWhat if we use the additive property to express f(x) for some x in terms of other values?\n\nAlternatively, let's consider f(1/x) = f(x)/x². Let's try to apply this for multiple values. For example, take x, then 1/x, then 1/(1/x) = x again. Let's apply condition (ii) twice. So first, f(1/x) = f(x)/x². Then, apply condition (ii) to 1/x: f(x) = f(1/x)/(1/x²) = f(1/x) * x². But substituting the first equation into this gives f(x) = (f(x)/x²) * x² = f(x), which is again a tautology.\n\nSo this doesn't give any new information. Hmm.\n\nAnother approach: Let's suppose f is additive and satisfies f(1/x) = f(x)/x². Let's compute f(1) in terms of f(1). Wait, as before, x=1 gives f(1) = f(1), so nothing. Let's take another value. Let's compute f(2). If f is additive, f(2) = 2f(1). Then f(1/2) = f(2)/4 = (2f(1))/4 = f(1)/2, which is consistent with f(1/2) = (1/2)f(1), which is the additive function.\n\nSimilarly, take x = 1/2. Then f(2) = f(1/2) * (1/(1/2))^2 = f(1/2) * 4. But we know f(2) = 2f(1) and f(1/2) = f(1)/2, so 2f(1) = 4*(f(1)/2) = 2f(1). So again, consistent.\n\nSo at least for rational multiples, it's consistent. But this seems to just confirm that linear functions work.\n\nBut how do we handle irrational numbers?\n\nSuppose x is irrational, and assume f(x) ≠ kx. Then f(1/x) = f(x)/x². But since x is irrational, 1/x is also irrational (unless x is ±1, but 1 is rational). So, we have two irrational numbers, x and 1/x, with f(1/x) related to f(x). But since additive functions can behave arbitrarily on irrationals unless restricted, perhaps this relation imposes a restriction.\n\nLet me suppose that f is additive and satisfies f(1/x) = f(x)/x² for all x ≠ 0. Let's try to see if f must be linear.\n\nAssume f is additive, so f(q) = q f(1) for all q ∈ ℚ. Now take an arbitrary real number x. We need to relate f(x) to f(1/x). Let's see.\n\nSuppose that f(x) = kx + g(x), where g(x) is some additive function that's zero on ℚ but non-zero on ℝ∖ℚ. But then, the condition f(1/x) = f(x)/x² would imply that k*(1/x) + g(1/x) = (kx + g(x))/x². Rearranging:\n\nk/x + g(1/x) = k/x + g(x)/x²\n\nSubtract k/x from both sides:\n\ng(1/x) = g(x)/x²\n\nBut if g is an additive function that's zero on ℚ, then for x irrational, g(x) is some value, and g(1/x) would be related to g(x) via the above equation.\n\nBut if g is additive and non-linear (i.e., not identically zero), can this equation hold?\n\nLet me consider a specific example. Suppose that g(x) = 0 for x ∈ ℚ and g(x) = c for x = α (an irrational), and extended additively. But additive functions require linearity over rationals, so actually, you can't just set g(α) = c; you have to define it over a basis. So perhaps using a Hamel basis. Let's suppose that we have a Hamel basis H for ℝ over ℚ, and g is defined by its values on H. Let’s pick a basis element h ∈ H, and define g(h) = c, and g(k) = 0 for other basis elements. Then for any real number x expressed as a finite linear combination x = q1*h1 + q2*h2 + ... + qn*hn with qi ∈ ℚ, then g(x) = q1*g(h1) + ... + qn*g(hn). So if h1 is the only one with g(h1) = c, then g(x) = c*q1.\n\nBut then, if we take x = h, then g(h) = c. Then, 1/h is not in the span of the Hamel basis unless h is ±1, but h is part of a Hamel basis, which typically includes elements not equal to 1. So 1/h would need to be expressed in terms of the basis. Suppose 1/h = q1*h1 + q2*h2 + ... + qn*hn. Then g(1/h) = q1*g(h1) + ... + qn*g(hn) = c*q1, assuming h1 = h. But then according to the condition, g(1/h) = g(h)/h² = c / h². But also, g(1/h) = c*q1. So c*q1 = c / h². If c ≠ 0, then q1 = 1 / h². But q1 is a rational number, and 1/h² is irrational (since h is irrational, unless h = 1 or -1, which are rational, but we already considered h as part of the Hamel basis, which would not include 1 if we are building it over ℚ). Wait, but 1 is in ℚ, so actually, 1 is already in the span (as the multiplicative identity). Hmm, maybe my previous assumption is flawed.\n\nAlternatively, maybe such a Hamel basis would include 1 as a basis element. Let's say 1 is part of the Hamel basis H. Then any real number can be expressed as a finite linear combination of basis elements with rational coefficients, including 1. So suppose h is another basis element, not 1. Then 1/h would need to be expressed in terms of the basis. But 1/h is not in the span of H if h is not 1. Wait, but 1/h is a real number, so it can be expressed as a combination of the basis elements. However, if H is a Hamel basis containing 1 and other elements, then 1/h would be expressed as q0*1 + q1*h1 + ... + qn*hn. Then, applying g to 1/h would be q0*g(1) + q1*g(h1) + ... + qn*g(hn). But if we defined g(1) = 0 (since f(1) = k*1 + g(1), but maybe not) — wait, this is getting complicated.\n\nAlternatively, suppose that g is identically zero on the Hamel basis except for some element h, but this might not satisfy the additive condition. Maybe such a function g cannot satisfy g(1/x) = g(x)/x² for all x ≠ 0 unless g is identically zero.\n\nSuppose that g is non-zero. Then there exists some x such that g(x) ≠ 0. Then from the condition g(1/x) = g(x)/x². Now, let's consider applying this condition multiple times. For example, take x and 1/x:\n\ng(1/x) = g(x)/x²,\n\nand then applying it again to 1/x:\n\ng(x) = g(1/x)/(1/x²) = g(1/x) * x².\n\nBut substituting the first equation into the second, we get:\n\ng(x) = (g(x)/x²) * x² = g(x), which is an identity.\n\nSo this doesn't give new information. How about applying it three times? Let me see:\n\nStart with g(x) = g(1/x) * x²,\n\nthen substitute g(1/x) = g(x)/x²,\n\nso we again get g(x) = (g(x)/x²) * x² = g(x). Still tautology.\n\nThis suggests that the condition is consistent but doesn't force g(x) to be zero. Hmm. Maybe we need another approach.\n\nLet me try to compute f(x) for some specific x.\n\nSuppose x = √2, an irrational number. Then f(√2) is something, and f(1/√2) = f(√2)/(√2)^2 = f(√2)/2.\n\nBut if f is additive, f(√2 + 1/√2) = f(√2) + f(1/√2) = f(√2) + f(√2)/2 = (3/2)f(√2).\n\nBut on the other hand, √2 + 1/√2 is another irrational number. If f is linear, then f(√2 + 1/√2) = k(√2 + 1/√2). But in the non-linear case, it's (3/2)f(√2). Is there a relation?\n\nAlternatively, maybe if we take higher powers or multiple applications of the functional equation.\n\nAlternatively, suppose we consider x and y such that x + y = 1/x. Hmm, not sure.\n\nAlternatively, use the Cauchy equation to express f in terms of f(1). For rational multiples, we know f(q) = q f(1). Maybe for real numbers, it's forced to be linear.\n\nWait, but without continuity, Cauchy's equation allows for non-linear solutions. However, the second condition might restrict things. Let's think.\n\nSuppose that f is additive and satisfies f(1/x) = f(x)/x². Let's compute f(x) for any x.\n\nLet me write x ≠ 0. Then f(1/x) = f(x)/x². Let's compute f(x) in terms of f(1/x):\n\nf(x) = x² f(1/x).\n\nBut also, since f is additive, for any integer n, f(n x) = n f(x). Let me try to combine this with the other condition.\n\nTake x = 1, n = 2: f(2) = 2 f(1). Then f(1/2) = f(2)/4 = (2 f(1))/4 = f(1)/2. Which is consistent with additivity.\n\nSimilarly, x = 1/2, then f(1/(1/2)) = f(2) = f(1/2)/( (1/2)^2 ) = (f(1)/2)/ (1/4) ) = 2 f(1). Which is correct.\n\nSo for rational numbers, everything is consistent. Now, for irrationals, let's see.\n\nSuppose that x is irrational. Then f(x) = x² f(1/x). But then f(1/x) = (1/x)^2 f(x). Wait, substituting x with 1/x, we get f(x) = x² f(1/x), which we already have. So that's redundant.\n\nBut combining with additivity, suppose we consider f(x + y) = f(x) + f(y). Let's suppose that x and y are such that x + y = 1/z for some z. Then f(1/z) = f(x) + f(y) = f(x) + f(1/z - x). But not sure if that helps.\n\nAlternatively, take y = 1/x, then f(x + 1/x) = f(x) + f(1/x) = f(x) + f(x)/x² = f(x)(1 + 1/x²). But we can also write x + 1/x = (x² + 1)/x. So f((x² + 1)/x) = f(x)(1 + 1/x²). Let's apply f to both sides:\n\nf((x² + 1)/x) = f(x) (1 + 1/x²)\n\nBut (x² + 1)/x = x + 1/x, which we already considered.\n\nAlternatively, let's write (x² + 1)/x = x + 1/x. Then, applying the additive property:\n\nf(x + 1/x) = f(x) + f(1/x) = f(x) + f(x)/x².\n\nBut also, f((x² + 1)/x) = f((x² + 1)/x). Since (x² + 1)/x is some real number. Let's write it as x + 1/x. Not sure.\n\nAlternatively, express (x² + 1)/x as (x²)/x + 1/x = x + 1/x, so that's back to the same thing.\n\nAlternatively, let's try to use the additive property on multiple variables. For example, consider f(x) = f(x * 1) = x f(1) if f is linear. But in the general additive case, this isn't necessarily true. But we have condition (ii), which relates f(1/x) and f(x).\n\nWait, suppose that f is additive and f(1/x) = f(x)/x² for all x ≠ 0. Let's consider f(1) = k.\n\nFor any x ≠ 0, f(x) = x² f(1/x). But f(1/x) is additive, so for x = q, a rational number, we have f(q) = q k, so f(1/q) = f(q)/q² = (q k)/q² = k /q, which is consistent with f(1/q) = k /q, as expected for additive functions.\n\nBut for irrational x, how does f(x) relate to x? Let's suppose that there's an irrational number α such that f(α) ≠ k α. Then f(1/α) = f(α)/α². But since α is irrational, 1/α is also irrational. Let's try to see if we can find a contradiction.\n\nIf f is additive and nonlinear, then for some α, f(α) ≠ k α. Then, f(1/α) = f(α)/α². Let's compute f(α + 1/α). By additivity, f(α + 1/α) = f(α) + f(1/α) = f(α) + f(α)/α² = f(α)(1 + 1/α²). But also, α + 1/α is some real number. Let's call it β. Then f(β) = f(α)(1 + 1/α²). But β is also irrational unless α + 1/α is rational, which would be a special case.\n\nBut in general, if f is nonlinear, there's no restriction on f(β) other than additivity. However, we can also apply the condition (ii) to β. That is, f(1/β) = f(β)/β². So:\n\nf(1/β) = [f(α)(1 + 1/α²)] / β².\n\nBut β = α + 1/α, so 1/β = 1/(α + 1/α). Let's compute this. Let me compute β = α + 1/α, so 1/β = α/(α² + 1). Then f(1/β) = f(α/(α² + 1)). \n\nNow, since f is additive, f(α/(α² + 1)) can be written as (α/(α² + 1)) f(1) if we assume that α/(α² + 1) is rational. But in general, α is irrational, and α/(α² + 1) is likely irrational as well. So f(α/(α² + 1)) could be something non-linear. But let's see if we can express this.\n\nBut also, from condition (ii), f(1/β) = f(β)/β². So, substituting β:\n\nf(1/β) = [f(β)] / β² = [f(α)(1 + 1/α²)] / β².\n\nBut β = α + 1/α, so β² = (α + 1/α)^2 = α² + 2 + 1/α². Therefore, β² = α² + 2 + 1/α². Hence,\n\nf(1/β) = [f(α)(1 + 1/α²)] / [α² + 2 + 1/α²].\n\nOn the other hand, 1/β = α/(α² + 1). So,\n\nf(1/β) = f(α/(α² + 1)) = f(α) / (α² + 1)² * [α² + 1]^2 ? No, wait, applying condition (ii) to x = α/(α² + 1) would give:\n\nf(1/(α/(α² + 1))) = f(α/(α² + 1)) / [ (α/(α² + 1))² ].\n\nBut 1/(α/(α² + 1)) = (α² + 1)/α = α + 1/α = β. So, this gives f(β) = [f(α/(α² + 1))]/[ (α/(α² + 1))² ].\n\nBut we already know f(β) = f(α)(1 + 1/α²). So,\n\nf(α)(1 + 1/α²) = [f(α/(α² + 1))]/[ (α/(α² + 1))² ]\n\n=> f(α/(α² + 1)) = f(α)(1 + 1/α²) * [ (α/(α² + 1))² ].\n\nBut α/(α² + 1) is 1/β, so:\n\nf(1/β) = f(α)(1 + 1/α²) * [ (α/(α² + 1))² ].\n\nBut we also have another expression for f(1/β) from the first part:\n\nf(1/β) = [f(α)(1 + 1/α²)] / [α² + 2 + 1/α²].\n\nThus, equating the two expressions for f(1/β):\n\n[ f(α)(1 + 1/α²) ] * [ α² / (α² + 1)^2 ] = [ f(α)(1 + 1/α²) ] / [ α² + 2 + 1/α² ]\n\nAssuming f(α) ≠ 0 and 1 + 1/α² ≠ 0 (which is true since α is real and non-zero), we can divide both sides by f(α)(1 + 1/α²):\n\nα² / (α² + 1)^2 = 1 / (α² + 2 + 1/α²)\n\nMultiply both sides by (α² + 1)^2 (α² + 2 + 1/α²):\n\nα² (α² + 2 + 1/α²) = (α² + 1)^2\n\nCompute left-hand side:\n\nα² * α² + 2α² + α²*(1/α²) = α^4 + 2α² + 1\n\nRight-hand side:\n\n(α² + 1)^2 = α^4 + 2α² + 1\n\nSo both sides are equal. Therefore, the equation is satisfied for any α ≠ 0. Therefore, this doesn't give us any new information. So even after substituting and using the equations, we end up with an identity.\n\nThis suggests that the condition (ii) is compatible with additive functions, but doesn't necessarily force them to be linear. However, perhaps if we iterate this process for multiple terms, we can derive a contradiction unless f is linear.\n\nAlternatively, let's consider x = √2 and compute f(√2). If f is additive, then f(√2) can be anything, but condition (ii) gives f(1/√2) = f(√2)/ ( (√2)^2 ) = f(√2)/2.\n\nBut since f is additive, f(√2 + √2) = 2 f(√2). But √2 + √2 = 2√2, which is another irrational number. Then f(2√2) = 2 f(√2). Similarly, applying condition (ii) to 2√2:\n\nf(1/(2√2)) = f(2√2)/ ( (2√2)^2 ) = (2 f(√2)) / (8) = f(√2)/4.\n\nBut 1/(2√2) = (1/2)(1/√2). Then f(1/(2√2)) = f( (1/2)(1/√2) ) = (1/2) f(1/√2) = (1/2)(f(√2)/2) = f(√2)/4.\n\nWhich is consistent. So this seems to check out even for irrational numbers, but it's just consistency with additivity and the condition (ii), not necessarily forcing linearity.\n\nWait a second. So even if f is a non-linear additive function, as long as it satisfies f(1/x) = f(x)/x², it's allowed. But can such a non-linear additive function exist?\n\nAlternatively, maybe the only additive functions satisfying f(1/x) = f(x)/x² are the linear functions f(x) = kx. Because when you iterate the condition, you might end up with f(x) = kx. Let's try to see.\n\nLet me suppose that for some x, f(x) = kx + g(x), where g(x) is a non-linear additive function. Then, from condition (ii):\n\nf(1/x) = k*(1/x) + g(1/x) = (kx + g(x))/x².\n\nThus,\n\nk/x + g(1/x) = k/x + g(x)/x².\n\nSubtracting k/x from both sides:\n\ng(1/x) = g(x)/x².\n\nBut g is an additive function. Suppose that g is not identically zero. Then, we have for any x ≠ 0:\n\ng(1/x) = g(x)/x².\n\nIf we can show that the only additive function satisfying this is g(x) = 0 for all x, then the only solution is f(x) = kx.\n\nAssume that g is additive and satisfies g(1/x) = g(x)/x² for all x ≠ 0. We need to show that g(x) = 0 for all x.\n\nFirst, note that for any rational number q, g(q) = q g(1). But since g(1) = g(1)/1² implies g(1) = g(1), which doesn't give new information. Wait, applying condition (ii) to x=1:\n\ng(1) = g(1)/1² ⇒ g(1) = g(1), which is always true. So g(1) can be anything? But wait, if g is additive, then for q ∈ ℚ, g(q) = q g(1). But if we take x = q, then g(1/q) = g(q)/q² = (q g(1))/q² = g(1)/q. But also, since 1/q is a rational number, g(1/q) = (1/q) g(1). Therefore:\n\n(1/q) g(1) = g(1)/q.\n\nWhich is an identity. So for rational numbers, it's consistent, but doesn't give any constraints. So g(1) is arbitrary.\n\nNow, let's consider an additive function g: ℝ → ℝ, which is zero on ℚ and non-zero on ℝ∖ℚ. Can such a function satisfy g(1/x) = g(x)/x² for all x ≠ 0?\n\nLet’s take x irrational. Then, g(x) is non-zero. Then, g(1/x) must be g(x)/x². But since x is irrational, 1/x is also irrational (unless x = ±1, which are rational). So 1/x is irrational. But as g is zero on ℚ and additive, for x irrational, g(x) can be non-zero. However, if g is non-zero, then the relation g(1/x) = g(x)/x² must hold. But since additive functions defined via Hamel basis can have arbitrary values on the basis elements, we need to check if such a relation is possible.\n\nSuppose we have a Hamel basis H, and h is an element of H. Suppose g(h) = c ≠ 0. Then, 1/h can be expressed as a finite linear combination of basis elements with rational coefficients. Let's write 1/h = q1*h1 + q2*h2 + ... + qn*hn, where hi are basis elements and qi are rational numbers. Then, g(1/h) = q1 g(h1) + ... + qn g(hn). If the only non-zero g(hi) is g(h) = c, then g(1/h) = q1 g(h1) + ... + qn g(hn). If h1 = h, then g(1/h) = q1 c + 0 + ... + 0 = q1 c. But according to condition (ii):\n\ng(1/h) = g(h)/h² = c / h².\n\nThus, we must have q1 c = c / h². Assuming c ≠ 0, we can divide both sides by c:\n\nq1 = 1 / h².\n\nBut q1 is a rational number, and 1/h² is irrational (since h is part of a Hamel basis, so h is irrational). This is a contradiction because the left side is rational and the right side is irrational. Therefore, such a function g cannot exist unless c = 0.\n\nTherefore, the only additive function g satisfying g(1/x) = g(x)/x² is the zero function. Hence, f(x) must be linear, i.e., f(x) = kx for some constant k.\n\nTherefore, the solutions are all linear functions f(x) = kx, where k is a real constant.\n</think>To find all functions \\( f: \\mathbb{R} \\to \\mathbb{R} \\) satisfying the conditions:\n1. \\( f(x + y) = f(x) + f(y) \\) for all real numbers \\( x \\) and \\( y \\).\n2. \\( f\\left(\\frac{1}{x}\\right) = \\frac{f(x)}{x^2} \\) for all \\( x \\neq 0 \\),\n\nwe proceed as follows:\n\n### Step 1: Consider the Cauchy Functional Equation\nThe first condition \\( f(x + y) = f(x) + f(y) \\) is known as Cauchy's functional equation. The general solutions to this equation are of the form \\( f(x) = kx \\) if we assume some regularity conditions (like continuity, monotonicity, or boundedness). Without such assumptions, the solutions can be non-linear (constructed via Hamel bases).\n\n### Step 2: Apply the Second Condition\nLet's check if the linear functions \\( f(x) = kx \\) satisfy the second condition \\( f\\left(\\frac{1}{x}\\right) = \\frac{f(x)}{x^2} \\).\n\nFor \\( f(x) = kx \\):\n\\[\nf\\left(\\frac{1}{x}\\right) = k \\cdot \\frac{1}{x} = \\frac{kx}{x^2} = \\frac{f(x)}{x^2}.\n\\]\nThus, linear functions \\( f(x) = kx \\) satisfy both conditions.\n\n### Step 3: Analyze Non-Linear Additive Functions\nNext, we need to determine if there are any non-linear additive functions that satisfy the second condition. Suppose \\( f \\) is an additive function, but not necessarily linear. Let \\( f(x) = kx + g(x) \\), where \\( g \\) is an additive function.\n\nFrom the second condition:\n\\[\nf\\left(\\frac{1}{x}\\right) = k \\cdot \\frac{1}{x} + g\\left(\\frac{1}{x}\\right) = \\frac{kx + g(x)}{x^2}.\n\\]\nSimplifying, we get:\n\\[\nk \\cdot \\frac{1}{x} + g\\left(\\frac{1}{x}\\right) = \\frac{kx + g(x)}{x^2}.\n\\]\nThis implies:\n\\[\ng\\left(\\frac{1}{x}\\right) = \\frac{g(x)}{x^2}.\n\\]\n\n### Step 4: Contradiction for Non-Linear \\( g \\)\nTo show that \\( g \\) must be zero, consider a Hamel basis \\( H \\) for \\( \\mathbb{R} \\) over \\( \\mathbb{Q} \\). Suppose \\( h \\in H \\) and \\( g(h) = c \\neq 0 \\). Then, \\( \\frac{1}{h} \\) can be expressed as a rational combination of basis elements:\n\\[\n\\frac{1}{h} = q_1 h_1 + q_2 h_2 + \\cdots + q_n h_n,\n\\]\nwhere \\( h_i \\in H \\) and \\( q_i \\in \\mathbb{Q} \\). By additivity:\n\\[\ng\\left(\\frac{1}{h}\\right) = q_1 g(h_1) + q_2 g(h_2) + \\cdots + q_n g(h_n).\n\\]\nIf \\( g \\) is non-zero on \\( h \\), then \\( g(h) = c \\neq 0 \\). This implies:\n\\[\ng\\left(\\frac{1}{h}\\right) = \\frac{c}{h^2}.\n\\]\nHowever, since \\( \\frac{1}{h} \\) is irrational and \\( h \\) is irrational, \\( \\frac{1}{h^2} \\) is irrational, while \\( q_i \\) are rational, leading to a contradiction. Therefore, \\( g \\) must be zero.\n\n### Conclusion\nThe only functions \\( f \\) that satisfy both conditions are the linear functions \\( f(x) = kx \\), where \\( k \\) is a real constant.\n\n\\[\n\\boxed{f(x) = kx \\text{ for some } k \\in \\mathbb{R}}\n\\]"}