{"row_number": 18638, "expected_answer": "\\( n-1 \\)", "problem": "Let \\( A = I - (x^* x)^{-1} (xx^*) \\), where \\( x \\) is a nonzero \\( n \\)-column vector and \\( x^* \\) denotes the conjugate transpose. Find the rank of \\( A \\).", "generated_solution": "<think>\nOkay, let me try to figure out the rank of matrix A. The problem says A is equal to I minus (x* x) inverse times x x*, where x is a nonzero n-column vector and x* is its conjugate transpose. Hmm, I need to find the rank of this matrix A.\n\nFirst, let me recall some linear algebra concepts. The rank of a matrix is the dimension of its column space, right? So, if I can figure out the column space of A, or maybe find out how many linearly independent columns (or rows) it has, that should give me the rank.\n\nGiven that x is a nonzero n-dimensional vector, x* would be a 1xn row vector. Then, x* x is like a scalar because it's a 1x1 matrix, right? Because when you multiply a 1xn vector with an nx1 vector, you get a scalar. So (x* x) is just the inner product of x with itself, which is a positive real number since x is nonzero. Therefore, (x* x)^{-1} is just the reciprocal of that scalar.\n\nOn the other hand, x x* is an nxn matrix. If x is a column vector, then x x* would be an outer product, resulting in an nxn matrix. The rank of x x* is 1 because the outer product of two vectors has rank 1 if both vectors are nonzero. Since x is nonzero, x x* has rank 1.\n\nSo, the matrix (x* x)^{-1} x x* is just scaling the matrix x x* by the reciprocal of the scalar (x* x). Since scaling a matrix by a non-zero scalar doesn't change its rank, the rank of (x* x)^{-1} x x* is still 1.\n\nTherefore, A is the identity matrix minus a rank 1 matrix. Now, what happens when we subtract a rank 1 matrix from the identity matrix? I think we need to find the rank of I - B where B is rank 1. \n\nLet me think about the rank of such a matrix. The identity matrix has full rank n. If we subtract a rank 1 matrix from it, the rank can decrease by at most 1 or increase by at most 1? Wait, no, actually, the rank of a sum or difference of two matrices can be tricky. But in this case, B has rank 1, and I has rank n. So, what is the rank of I - B?\n\nAlternatively, maybe there's a better approach. Let's think about the image of A. If we can find the image of A, which is the set of all vectors A v for any vector v in the space, then the dimension of that image is the rank.\n\nAlternatively, consider that A is I - (x x*) / (x* x). Let me denote (x x*) / (x* x) as a projection matrix. Wait, projection matrices have the property that they are idempotent, meaning that P^2 = P. Let me check if (x x*) / (x* x) is a projection matrix.\n\nLet’s compute P^2 where P = (x x*) / (x* x). Then,\n\nP^2 = (x x*) / (x* x) * (x x*) / (x* x) \n\nFirst, multiply the two x x* terms. Let's compute x x* x x*. Since matrix multiplication is associative, this is x (x* x) x*. Since x* x is a scalar, say c, then this becomes x c x* = c x x*. So P^2 = (c x x*) / (c^2) ) = (x x*) / c = P. Therefore, yes, P is indeed a projection matrix. So, P is the orthogonal projection onto the column space of x, which is a one-dimensional subspace. Therefore, I - P is the projection onto the orthogonal complement of the column space of x. So, the rank of I - P would be n - 1, since the orthogonal complement of a one-dimensional subspace in an n-dimensional space has dimension n - 1. \n\nTherefore, the rank of A = I - P is n - 1.\n\nWait, but let me verify this. Let's take an example. Suppose x is a vector in 3-dimensional space, say x = [1; 0; 0]. Then x* is [1, 0, 0], so x* x = 1. Then P = (x x*) / (x* x) = x x*, which is a matrix with 1 in the (1,1) entry and 0 elsewhere. Then I - P is a diagonal matrix with 0 in the (1,1) entry and 1 elsewhere. The rank of this matrix is 2, which is 3 - 1. So yes, that checks out.\n\nAnother example: take x = [1; 1] in 2D. Then x* x = 1 + 1 = 2. Then P = (1/2) * [1 1; 1 1]. Then I - P is [1 - 1/2, -1/2; -1/2, 1 - 1/2] = [1/2, -1/2; -1/2, 1/2]. The rank of this matrix is 1, since the rows are multiples of each other. Wait, but 2 - 1 = 1, so that's also correct. Wait, but in this case, rank of I - P is 1, which is 2 - 1. Hmm, but in the first example, it was 3 -1 =2. So that seems consistent. Therefore, the rank of I - P is n - 1.\n\nTherefore, the answer should be n - 1.\n\nBut let me see if there's another way to think about it. Suppose v is any vector. Then A v = v - (x* x)^{-1} x x* v. Let's write that as v - (x* v / x* x) x. Which is like subtracting the projection of v onto x from v. So A is projecting v onto the orthogonal complement of x. Therefore, the image of A is the orthogonal complement of x, which has dimension n - 1. Therefore, rank of A is n - 1.\n\nAlternatively, the null space of A would be the set of vectors v such that A v = 0. So, v - (x* x)^{-1} x x* v = 0. Which implies that v = (x* x)^{-1} x x* v. Let c = (x* x)^{-1} x* v, which is a scalar. Then v = c x. So the null space of A is the span of x, which is one-dimensional. By the rank-nullity theorem, rank(A) + nullity(A) = n. Since nullity is 1, the rank is n - 1.\n\nYes, that also confirms it. So regardless of the approach, the rank of A is n - 1. Therefore, the answer is n - 1.\n\n**Final Answer**\nThe rank of \\( A \\) is \\boxed{n - 1}.\n</think>To find the rank of the matrix \\( A = I - (x^* x)^{-1} (xx^*) \\), where \\( x \\) is a nonzero \\( n \\)-column vector and \\( x^* \\) denotes the conjugate transpose, we proceed as follows:\n\n1. **Identify the components**:\n   - \\( x^* x \\) is a scalar, specifically the inner product of \\( x \\) with itself, which is a positive real number since \\( x \\) is nonzero.\n   - \\((x^* x)^{-1}\\) is the reciprocal of this scalar.\n   - \\( xx^* \\) is an \\( n \\times n \\) matrix, which is the outer product of \\( x \\) with itself. Since \\( x \\) is nonzero, the rank of \\( xx^* \\) is 1.\n\n2. **Projection Matrix**:\n   - Define \\( P = \\frac{xx^*}{x^* x} \\). We can verify that \\( P \\) is a projection matrix by checking \\( P^2 = P \\):\n     \\[\n     P^2 = \\left( \\frac{xx^*}{x^* x} \\right) \\left( \\frac{xx^*}{x^* x} \\right) = \\frac{xx^* (x^* x)}{(x^* x)^2} = \\frac{xx^*}{x^* x} = P.\n     \\]\n   - Since \\( P \\) is a projection matrix, it projects any vector onto the span of \\( x \\). Therefore, the rank of \\( P \\) is 1.\n\n3. **Matrix \\( A \\)**:\n   - The matrix \\( A = I - P \\) is the projection onto the orthogonal complement of the column space of \\( x \\). Since \\( P \\) projects onto a 1-dimensional subspace, \\( I - P \\) projects onto the orthogonal complement, which is \\( n-1 \\) dimensional.\n\n4. **Rank Analysis**:\n   - The null space of \\( A \\) consists of vectors \\( v \\) such that \\( A v = 0 \\). This implies:\n     \\[\n     v - \\frac{x^* x}{x^* x} (x^* v) x = 0 \\implies v = (x^* v) x.\n     \\]\n     Therefore, the null space of \\( A \\) is the span of \\( x \\), which is 1-dimensional.\n   - By the rank-nullity theorem, the rank of \\( A \\) plus the nullity of \\( A \\) equals \\( n \\):\n     \\[\n     \\text{rank}(A) + \\text{nullity}(A) = n.\n     \\]\n     Since the nullity of \\( A \\) is 1, the rank of \\( A \\) is \\( n - 1 \\).\n\nThus, the rank of \\( A \\) is \\(\\boxed{n - 1}\\)."}