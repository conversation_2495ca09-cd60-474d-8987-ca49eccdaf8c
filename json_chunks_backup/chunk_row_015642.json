{"row_number": 15642, "expected_answer": "\\(-\\ln 2\\)", "problem": "Compute $\\sum_{n=2}^{\\infty} \\ln\\left(1 - \\frac{1}{n^2}\\right)$.", "generated_solution": "<think>\nOkay, so I need to compute the infinite sum of the natural logarithm of (1 - 1/n²) starting from n=2 to infinity. Hmm, let me think about how to approach this. \n\nFirst, I remember that sometimes when dealing with logarithms in a series, you can use properties of logarithms to simplify the expression. Specifically, the logarithm of a product can be turned into a sum of logarithms. But here, we have a sum of logarithms, so maybe we can reverse that and turn it into the logarithm of a product. That might help if the product telescopes or simplifies in some way. \n\nSo, the original sum is Σ_{n=2}^∞ ln(1 - 1/n²). Let me write that out as ln(1 - 1/2²) + ln(1 - 1/3²) + ln(1 - 1/4²) + ... and so on. If I convert the sum of logs into the log of a product, that would be ln[ product_{n=2}^∞ (1 - 1/n²) ]. \n\nNow, I need to figure out what this infinite product is. Let me recall that 1 - 1/n² can be factored using the difference of squares. So, 1 - 1/n² = (1 - 1/n)(1 + 1/n). Therefore, the product becomes product_{n=2}^∞ (1 - 1/n)(1 + 1/n). \n\nSo, that's the same as [product_{n=2}^∞ (1 - 1/n)] * [product_{n=2}^∞ (1 + 1/n)]. Let me look at each of these products separately. \n\nStarting with the first product: product_{n=2}^∞ (1 - 1/n). Let's write out the terms for a few n to see if there's a pattern. \n\nFor n=2: 1 - 1/2 = 1/2\n\nFor n=3: 1 - 1/3 = 2/3\n\nFor n=4: 1 - 1/4 = 3/4\n\nFor n=5: 1 - 1/5 = 4/5\n\nSo, multiplying these together: (1/2)(2/3)(3/4)(4/5)... \n\nI notice that this is telescoping. Each numerator cancels with the denominator of the next term. For example, the 2 in the numerator of the second term cancels with the 2 in the denominator of the first term, the 3 cancels with the denominator of the second term, etc. So, all terms cancel except the first denominator and the last numerator. But since this is an infinite product, the last term approaches infinity, so the limit of the product as n approaches infinity would be the limit of 1/n as n approaches infinity. Therefore, product_{n=2}^∞ (1 - 1/n) = lim_{N→∞} (1/2)(2/3)(3/4)...( (N-1)/N ) ) = lim_{N→∞} 1/N = 0.\n\nWait, but that's product_{n=2}^N (1 - 1/n) = 1/N. So as N approaches infinity, it approaches 0. Hmm, but that's just the first product.\n\nNow, let's look at the second product: product_{n=2}^∞ (1 + 1/n). Writing out the terms:\n\nFor n=2: 1 + 1/2 = 3/2\n\nFor n=3: 1 + 1/3 = 4/3\n\nFor n=4: 1 + 1/4 = 5/4\n\nFor n=5: 1 + 1/5 = 6/5\n\nSo, multiplying these: (3/2)(4/3)(5/4)(6/5)... \n\nAgain, telescoping occurs. The 3 in the numerator cancels with the denominator of the next term, 4 cancels, etc. So, this product telescopes to lim_{N→∞} (N+1)/2. Because when you multiply up to n=N, the product would be (N+1)/2. So as N approaches infinity, this product diverges to infinity.\n\nBut wait, we have the original product as [product (1 - 1/n)] * [product (1 + 1/n)] which is (1/N) * (N+1)/2 = (N+1)/(2N). As N approaches infinity, that's (N+1)/(2N) = (1 + 1/N)/2 → 1/2 as N approaches infinity. \n\nAh! So even though each product individually either goes to zero or infinity, their product might converge. Let me verify that again.\n\nSo combining the two products:\n\nproduct_{n=2}^N (1 - 1/n²) = [product_{n=2}^N (1 - 1/n)] * [product_{n=2}^N (1 + 1/n)].\n\nAs we saw, product_{n=2}^N (1 - 1/n) = 1/N, and product_{n=2}^N (1 + 1/n) = (N+1)/2.\n\nMultiplying those gives (1/N)*(N+1)/2 = (N+1)/(2N) = (1/2)(1 + 1/N).\n\nSo, as N approaches infinity, 1/N approaches 0, so this product approaches (1/2)(1 + 0) = 1/2. Therefore, the infinite product is 1/2.\n\nTherefore, the original sum is the natural logarithm of that product, which is ln(1/2). \n\nBut wait, let me double-check. If the product from n=2 to infinity of (1 - 1/n²) equals 1/2, then the sum of ln(1 - 1/n²) from n=2 to infinity is ln(1/2) which is -ln(2).\n\nIs that correct? Let me verify with partial products.\n\nTake N=2: product is 1 - 1/4 = 3/4. \n\nN=3: 3/4 * (1 - 1/9) = 3/4 * 8/9 = 24/36 = 2/3.\n\nN=4: 2/3 * (15/16) = 30/48 = 5/8.\n\nWait, but according to the formula (N+1)/(2N), when N=2, (2+1)/(2*2) = 3/4. Correct. N=3: 4/(2*3) = 4/6 = 2/3. Correct. N=4: 5/(2*4)=5/8. Correct. So that formula is correct. So as N approaches infinity, it's approaching 1/2. \n\nTherefore, yes, the product converges to 1/2, so the logarithm is ln(1/2) which is equal to -ln(2). \n\nTherefore, the sum converges to -ln(2). \n\nWait, but I should check whether the step where I split the product into two products is valid. That is, product_{n=2}^∞ (1 - 1/n²) = [product_{n=2}^∞ (1 - 1/n)] * [product_{n=2}^∞ (1 + 1/n)]. \n\nBut each of those individual products might not converge, but their combination does. Is that a valid operation?\n\nIn general, if you have two infinite products where one diverges to zero and the other diverges to infinity, their product could potentially converge. But when dealing with infinite products, you have to be careful. However, in this case, since we're looking at the partial products up to N, and when multiplied together, they converge to 1/2, then it's valid. \n\nAlternatively, another way to compute the product is to recognize that:\n\nproduct_{n=2}^N (1 - 1/n²) = product_{n=2}^N [(n - 1)(n + 1)/n²] = product_{n=2}^N (n - 1)/n * product_{n=2}^N (n + 1)/n.\n\nBreaking that into two products:\n\nFirst product: product_{n=2}^N (n - 1)/n = [1/2][2/3][3/4]...[(N-1)/N] = 1/N.\n\nSecond product: product_{n=2}^N (n + 1)/n = [3/2][4/3][5/4]...[(N+1)/N] = (N+1)/2.\n\nMultiplying these gives (1/N)(N+1)/2 = (N+1)/(2N) → 1/2 as N → ∞.\n\nTherefore, the product indeed converges to 1/2, so the sum of the logarithms is ln(1/2) = -ln(2).\n\nTherefore, the answer should be -ln(2). But let me check with another approach to be sure.\n\nAnother approach: Expand the general term ln(1 - 1/n²). \n\nWe can write ln(1 - 1/n²) = ln[(n - 1)(n + 1)/n²] = ln(n - 1) + ln(n + 1) - 2 ln n.\n\nTherefore, the sum from n=2 to infinity is Σ [ln(n - 1) + ln(n + 1) - 2 ln n].\n\nLet me write this as Σ [ln(n - 1) - ln n] + Σ [ln(n + 1) - ln n].\n\nSo split the sum into two separate sums:\n\nFirst sum: Σ_{n=2}^∞ [ln(n - 1) - ln n]\n\nSecond sum: Σ_{n=2}^∞ [ln(n + 1) - ln n]\n\nNow, let's analyze each sum.\n\nFirst sum: Let's index shift. Let k = n - 1. Then when n=2, k=1. So the first sum becomes Σ_{k=1}^∞ [ln k - ln(k + 1)]. That's a telescoping series. Each term is ln k - ln(k + 1). The partial sum from k=1 to m is ln 1 - ln(m + 1). Since ln 1 = 0, the partial sum is -ln(m + 1). As m approaches infinity, that goes to -infinity. Wait, but that suggests the first sum diverges to -infinity. Hmm, but we need to check if that's the case. Similarly, the second sum.\n\nWait, but hold on. Maybe the combination of the two sums cancels out the divergence?\n\nWait, let's check the second sum: Σ_{n=2}^∞ [ln(n + 1) - ln n]. Let's index shift here as well. Let k = n + 1. When n=2, k=3. So the sum becomes Σ_{k=3}^∞ [ln k - ln(k - 1)]. Which telescopes as well. The partial sum from k=3 to m is ln m - ln 2. As m approaches infinity, that goes to infinity - ln 2, which diverges to infinity.\n\nSo combining the two sums: first sum tends to -infinity, second sum tends to infinity, so their combination is (-infinity) + infinity, which is indeterminate. But maybe when combined properly, they can cancel.\n\nWait, but this seems contradictory to the previous result where the sum was -ln 2. So perhaps splitting the sum into these two divergent series isn't helpful. But maybe if we combine terms more cleverly.\n\nWait, let's write out the sum S = Σ_{n=2}^∞ [ln(n - 1) + ln(n + 1) - 2 ln n]. Let's write this as Σ_{n=2}^∞ [ln(n - 1) - ln n] + Σ_{n=2}^∞ [ln(n + 1) - ln n].\n\nBut perhaps instead of treating them separately, we can look at the partial sum up to N and see how it telescopes.\n\nLet's compute the partial sum S_N = Σ_{n=2}^N [ln(n - 1) + ln(n + 1) - 2 ln n].\n\nLet's write each term as:\n\nln(n - 1) - ln n + ln(n + 1) - ln n.\n\nSo, S_N = Σ_{n=2}^N [ln(n - 1) - ln n] + Σ_{n=2}^N [ln(n + 1) - ln n].\n\nNow, first part of S_N: Σ_{n=2}^N [ln(n - 1) - ln n] = [ln 1 - ln 2] + [ln 2 - ln 3] + ... + [ln(N-1) - ln N]. This telescopes to ln 1 - ln N = 0 - ln N = -ln N.\n\nSecond part of S_N: Σ_{n=2}^N [ln(n + 1) - ln n] = [ln 3 - ln 2] + [ln 4 - ln 3] + ... + [ln(N+1) - ln N]. This telescopes to ln(N+1) - ln 2.\n\nTherefore, the total partial sum S_N = (-ln N) + (ln(N+1) - ln 2) = ln(N+1) - ln N - ln 2.\n\nSimplify this: ln((N+1)/N) - ln 2 = ln(1 + 1/N) - ln 2.\n\nAs N approaches infinity, ln(1 + 1/N) approaches ln(1) = 0. Therefore, the limit of S_N as N approaches infinity is 0 - ln 2 = -ln 2.\n\nTherefore, the sum converges to -ln 2. Which matches the previous result. \n\nTherefore, regardless of the method used, whether converting the sum into a logarithm of a product which telescopes, or expanding the logarithm and splitting the sum into telescoping parts, we arrive at the same conclusion that the sum is -ln 2.\n\nThus, the final answer is -ln 2, which in boxed form is \\boxed{-\\ln 2}.\n</think>To compute the sum \\(\\sum_{n=2}^{\\infty} \\ln\\left(1 - \\frac{1}{n^2}\\right)\\), we start by converting the sum into the logarithm of a product:\n\n\\[\n\\sum_{n=2}^{\\infty} \\ln\\left(1 - \\frac{1}{n^2}\\right) = \\ln\\left( \\prod_{n=2}^{\\infty} \\left(1 - \\frac{1}{n^2}\\right) \\right).\n\\]\n\nNext, we factor \\(1 - \\frac{1}{n^2}\\) using the difference of squares:\n\n\\[\n1 - \\frac{1}{n^2} = \\left(1 - \\frac{1}{n}\\right)\\left(1 + \\frac{1}{n}\\right).\n\\]\n\nThus, the product becomes:\n\n\\[\n\\prod_{n=2}^{\\infty} \\left(1 - \\frac{1}{n^2}\\right) = \\prod_{n=2}^{\\infty} \\left(1 - \\frac{1}{n}\\right) \\prod_{n=2}^{\\infty} \\left(1 + \\frac{1}{n}\\right).\n\\]\n\nWe analyze each product separately. For the first product:\n\n\\[\n\\prod_{n=2}^{N} \\left(1 - \\frac{1}{n}\\right) = \\prod_{n=2}^{N} \\frac{n-1}{n} = \\frac{1}{2} \\cdot \\frac{2}{3} \\cdot \\frac{3}{4} \\cdots \\frac{N-1}{N} = \\frac{1}{N}.\n\\]\n\nAs \\(N \\to \\infty\\), this product converges to 0:\n\n\\[\n\\lim_{N \\to \\infty} \\prod_{n=2}^{N} \\left(1 - \\frac{1}{n}\\right) = \\lim_{N \\to \\infty} \\frac{1}{N} = 0.\n\\]\n\nFor the second product:\n\n\\[\n\\prod_{n=2}^{N} \\left(1 + \\frac{1}{n}\\right) = \\prod_{n=2}^{N} \\frac{n+1}{n} = \\frac{3}{2} \\cdot \\frac{4}{3} \\cdot \\frac{5}{4} \\cdots \\frac{N+1}{N} = \\frac{N+1}{2}.\n\\]\n\nAs \\(N \\to \\infty\\), this product diverges to \\(\\infty\\):\n\n\\[\n\\lim_{N \\to \\infty} \\prod_{n=2}^{N} \\left(1 + \\frac{1}{n}\\right) = \\lim_{N \\to \\infty} \\frac{N+1}{2} = \\infty.\n\\]\n\nHowever, combining these products:\n\n\\[\n\\prod_{n=2}^{N} \\left(1 - \\frac{1}{n^2}\\right) = \\left( \\prod_{n=2}^{N} \\left(1 - \\frac{1}{n}\\right) \\right) \\left( \\prod_{n=2}^{N} \\left(1 + \\frac{1}{n}\\right) \\right) = \\frac{1}{N} \\cdot \\frac{N+1}{2} = \\frac{N+1}{2N}.\n\\]\n\nAs \\(N \\to \\infty\\), this product converges to \\(\\frac{1}{2}\\):\n\n\\[\n\\lim_{N \\to \\infty} \\frac{N+1}{2N} = \\frac{1}{2}.\n\\]\n\nThus, the infinite product converges to \\(\\frac{1}{2}\\), and the sum is:\n\n\\[\n\\ln\\left( \\frac{1}{2} \\right) = -\\ln(2).\n\\]\n\nTherefore, the final answer is:\n\n\\[\n\\boxed{-\\ln 2}.\n\\]"}