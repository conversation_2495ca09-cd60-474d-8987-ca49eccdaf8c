{"row_number": 15907, "expected_answer": "1560", "problem": "How many onto functions are there from a set with 6 elements to a set with 4 elements?", "generated_solution": "<think>\nOkay, so I need to find the number of onto functions from a set with 6 elements to a set with 4 elements. Hmm, onto functions mean that every element in the 4-element set has at least one preimage in the 6-element set, right? So, no element in the codomain is left out. \n\nI remember something about using the principle of inclusion-exclusion for these kinds of problems. Let me recall. The formula for the number of onto functions from a set A with m elements to a set B with n elements is given by:\n\n$$\n\\sum_{k=0}^{n} (-1)^k \\binom{n}{k} (n - k)^m\n$$\n\nWait, no, let me check. Maybe it's:\n\n$$\n\\sum_{k=0}^{n} (-1)^k \\binom{n}{k} (n - k)^m\n$$\n\nBut actually, the formula is the inclusion-exclusion where we subtract the cases where one or more elements are not mapped to. So yes, the formula should be:\n\n$$\n\\sum_{k=0}^{n} (-1)^k \\binom{n}{k} (n - k)^m\n$$\n\nBut wait, n is 4 here and m is 6. Let me plug in the numbers. \n\nSo, substituting n=4 and m=6, the number of onto functions would be:\n\n$$\n\\sum_{k=0}^{4} (-1)^k \\binom{4}{k} (4 - k)^6\n$$\n\nLet me compute each term step by step.\n\nFor k=0:\n\n$$\n(-1)^0 \\binom{4}{0} (4 - 0)^6 = 1 * 1 * 4^6 = 4096\n$$\n\nFor k=1:\n\n$$\n(-1)^1 \\binom{4}{1} (4 - 1)^6 = -1 * 4 * 3^6 = -4 * 729 = -2916\n$$\n\nWait, 3^6 is 729? Let me verify that. 3^2=9, 3^3=27, 3^4=81, 3^5=243, 3^6=729. Yes, that's right. So -4*729 = -2916.\n\nFor k=2:\n\n$$\n(-1)^2 \\binom{4}{2} (4 - 2)^6 = 1 * 6 * 2^6 = 6 * 64 = 384\n$$\n\nWait, 2^6 is 64, yes. 6*64 is 384.\n\nFor k=3:\n\n$$\n(-1)^3 \\binom{4}{3} (4 - 3)^6 = -1 * 4 * 1^6 = -4 * 1 = -4\n$$\n\nAnd for k=4:\n\n$$\n(-1)^4 \\binom{4}{4} (4 - 4)^6 = 1 * 1 * 0^6 = 0\n$$\n\nBecause 0^6 is 0. So that term is 0.\n\nNow, adding all these up:\n\n4096 (k=0) - 2916 (k=1) + 384 (k=2) - 4 (k=3) + 0 (k=4)\n\nCalculating step by step:\n\nFirst, 4096 - 2916 = 1180\n\nThen, 1180 + 384 = 1564\n\nThen, 1564 - 4 = 1560\n\nSo the total number of onto functions is 1560.\n\nWait, let me double-check the calculations because it's easy to make arithmetic errors here.\n\nStarting with 4096 minus 2916. \n\n4096 - 2000 = 2096\n\n2096 - 916 = 1180. Yeah, that's correct.\n\nThen 1180 + 384. Let's break it down: 1180 + 300 = 1480, then +84 = 1564. Right.\n\nThen 1564 - 4 is 1560. That seems correct.\n\nSo the answer should be 1560 onto functions.\n\nAlternatively, another way to check: use the formula for surjective functions. The formula is indeed the inclusion-exclusion sum. Let me recall the Stirling numbers of the second kind, which count the number of ways to partition a set into non-empty subsets. Then, the number of onto functions is S(m, n) * n! where m is the number of elements in the domain and n in the codomain.\n\nSo, S(6, 4) * 4! \n\nIf I can compute S(6,4) and multiply by 24 (since 4! is 24), that should give the same result.\n\nWhat's S(6,4)? The Stirling number of the second kind for 6 elements and 4 subsets.\n\nThe formula for Stirling numbers of the second kind is:\n\n$$\nS(m, n) = \\frac{1}{n!} \\sum_{k=0}^{n} (-1)^{n-k} \\binom{n}{k} k^m\n$$\n\nBut that might complicate things. Alternatively, I remember that S(6,4) can be computed recursively or using known values.\n\nAlternatively, maybe I can compute S(6,4):\n\nThe formula is S(m, n) = S(m-1, n-1) + n*S(m-1, n)\n\nSo starting from the base cases:\n\nS(0,0) = 1\n\nS(n,0) = 0 for n > 0\n\nS(0,k) = 0 for k > 0\n\nS(n,n) = 1\n\nS(n,1) = 1\n\nS(n,2) = 2^{n-1} -1\n\nWait, maybe there's a table of Stirling numbers. Let me build up from lower numbers.\n\nCompute S(1,1)=1\n\nS(2,1)=1, S(2,2)=1\n\nS(3,1)=1, S(3,2)=3, S(3,3)=1\n\nS(4,1)=1, S(4,2)=7, S(4,3)=6, S(4,4)=1\n\nWait, S(4,3)=6?\n\nWait, let me check: How many ways to partition 4 elements into 3 subsets. It should be C(4,2) = 6, since you choose two elements to be together and the rest are singletons. Yes, that's correct. So S(4,3)=6.\n\nSimilarly, S(4,2)=7. Because the number of ways to partition 4 elements into 2 subsets: either 1 and 3 elements or 2 and 2. The number is 7. For 1 and 3: C(4,1)=4. For 2 and 2: C(4,2)/2=3. So total 7.\n\nThen S(5,4). Using the recurrence: S(5,4) = S(4,3) + 4*S(4,4) = 6 + 4*1 = 10.\n\nThen S(6,4) = S(5,3) + 4*S(5,4). So we need S(5,3) and S(5,4).\n\nFirst, compute S(5,3):\n\nS(5,3) = S(4,2) + 3*S(4,3) = 7 + 3*6 = 7 + 18 = 25.\n\nThen S(5,4) = 10 as above.\n\nTherefore, S(6,4) = 25 + 4*10 = 25 + 40 = 65.\n\nTherefore, the number of onto functions is 65 * 4! = 65 * 24 = 1560. Which matches the previous result. So that's a good cross-check.\n\nTherefore, the answer is 1560.\n</think>To find the number of onto functions from a set with 6 elements to a set with 4 elements, we use the principle of inclusion-exclusion. The formula for the number of onto functions from a set of size \\( m \\) to a set of size \\( n \\) is given by:\n\n\\[\n\\sum_{k=0}^{n} (-1)^k \\binom{n}{k} (n - k)^m\n\\]\n\nHere, \\( m = 6 \\) and \\( n = 4 \\). Substituting these values into the formula, we get:\n\n\\[\n\\sum_{k=0}^{4} (-1)^k \\binom{4}{k} (4 - k)^6\n\\]\n\nWe will calculate each term in the sum separately:\n\n1. For \\( k = 0 \\):\n\\[\n(-1)^0 \\binom{4}{0} (4 - 0)^6 = 1 \\cdot 1 \\cdot 4^6 = 4096\n\\]\n\n2. For \\( k = 1 \\):\n\\[\n(-1)^1 \\binom{4}{1} (4 - 1)^6 = -1 \\cdot 4 \\cdot 3^6 = -1 \\cdot 4 \\cdot 729 = -2916\n\\]\n\n3. For \\( k = 2 \\):\n\\[\n(-1)^2 \\binom{4}{2} (4 - 2)^6 = 1 \\cdot 6 \\cdot 2^6 = 1 \\cdot 6 \\cdot 64 = 384\n\\]\n\n4. For \\( k = 3 \\):\n\\[\n(-1)^3 \\binom{4}{3} (4 - 3)^6 = -1 \\cdot 4 \\cdot 1^6 = -1 \\cdot 4 \\cdot 1 = -4\n\\]\n\n5. For \\( k = 4 \\):\n\\[\n(-1)^4 \\binom{4}{4} (4 - 4)^6 = 1 \\cdot 1 \\cdot 0^6 = 0\n\\]\n\nSumming these terms together:\n\n\\[\n4096 - 2916 + 384 - 4 + 0 = 1560\n\\]\n\nThus, the number of onto functions from a set with 6 elements to a set with 4 elements is \\(\\boxed{1560}\\)."}