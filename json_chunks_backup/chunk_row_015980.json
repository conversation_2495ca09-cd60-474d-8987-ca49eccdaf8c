{"row_number": 15980, "expected_answer": "\\(\\binom{n+4}{4}\\)", "problem": "How many quadruples \\((a, b, c, d)\\) of non-negative integers satisfy the condition \\(0 \\leq a \\leq b \\leq c \\leq d \\leq n\\)?", "generated_solution": "<think>\nOkay, let's see. I need to find the number of quadruples (a, b, c, d) where each is a non-negative integer and they satisfy 0 ≤ a ≤ b ≤ c ≤ d ≤ n. Hmm. So, all four numbers are in non-decreasing order, right? And each of them can be from 0 up to n, but they have to stay in that order: a is the smallest, then b is at least a, c at least b, d at least c, and d can't exceed n. \n\nI remember that when you have variables that are ordered like this, you can use a combinatorial concept called \"stars and bars.\" Let me recall how that works. The stars and bars theorem is used to find the number of ways to distribute indistinguishable objects into distinguishable bins, but here we want the number of ordered tuples with non-decreasing elements. Maybe there's a way to convert one problem into the other.\n\nWait, in the stars and bars formula, the number of non-negative integer solutions to x1 + x2 + ... + xk = n is C(n + k - 1, k - 1). But how does that relate to ordered quadruples?\n\nAlternatively, I remember that the number of non-decreasing sequences of length k where each element is between 0 and n (inclusive) is equivalent to the number of combinations with repetition. The formula for that is C(n + k, k). Wait, let me check.\n\nSuppose we have variables a ≤ b ≤ c ≤ d ≤ n. If we let a, b, c, d be non-decreasing, this is like choosing 4 elements from a set of size n+1 (since they go from 0 to n) with repetition allowed. Because when you allow repetition, the number of combinations is C(n + 4, 4). Wait, is that right?\n\nWait, no. Wait, the formula for combinations with repetition is C(n + k, k) where n is the number of types and k is the number of selections. So in this case, the \"types\" are the numbers from 0 to n, which is n+1 types. And we are selecting 4 elements with repetition allowed, where order doesn't matter. But in our problem, the order does matter in the sense that we are considering sequences, but they are non-decreasing. So, actually, each combination with repetition corresponds to exactly one non-decreasing sequence. So the number of such sequences should be C(n + 4, 4). Wait, but let me verify.\n\nWait, for example, if n=1, then possible quadruples would be (0,0,0,0), (0,0,0,1), (0,0,1,1), (0,1,1,1), (1,1,1,1). Let's count them. That's 5. If we compute C(1 + 4,4) = C(5,4)=5. So that works. Let's check another case. If n=2, then the number should be C(2+4,4)=C(6,4)=15. Let's see if that's correct. The quadruples would be all combinations where 0 ≤ a ≤ b ≤ c ≤ d ≤ 2. That should include all sequences where the numbers are 0s, 1s, and 2s in non-decreasing order. For example:\n\n(0,0,0,0), (0,0,0,1), (0,0,0,2),\n\n(0,0,1,1), (0,0,1,2),\n\n(0,0,2,2),\n\n(0,1,1,1), (0,1,1,2),\n\n(0,1,2,2),\n\n(0,2,2,2),\n\n(1,1,1,1), (1,1,1,2),\n\n(1,1,2,2),\n\n(1,2,2,2),\n\n(2,2,2,2).\n\nCounting these: 1 (all 0s), then for ones with a=0:\n\n- d=0: 1\n\n- d=1: 2\n\n- d=2: 3\n\nBut wait, actually in the list above, I have 1 (all 0s), then for d=1: 0,0,0,1 and 0,0,1,1? Wait, maybe my count was wrong. Let me list all:\n\n1. 0,0,0,0\n\n2. 0,0,0,1\n\n3. 0,0,0,2\n\n4. 0,0,1,1\n\n5. 0,0,1,2\n\n6. 0,0,2,2\n\n7. 0,1,1,1\n\n8. 0,1,1,2\n\n9. 0,1,2,2\n\n10. 0,2,2,2\n\n11. 1,1,1,1\n\n12. 1,1,1,2\n\n13. 1,1,2,2\n\n14. 1,2,2,2\n\n15. 2,2,2,2\n\nYes, that's 15. So that works. So the formula C(n + 4, 4) gives the correct result here. So for general n, the number of quadruples is C(n + 4, 4). Wait, but hold on. Let me confirm again.\n\nWait, in the formula for combinations with repetition, it's C(n + k, k), where n is the number of types. But in our case, the numbers can go up to n, so the number of types is n + 1 (since 0 is included). Therefore, the formula should be C((n + 1) + 4 - 1, 4) = C(n + 4, 4). Yes, that's correct. Because the formula for combinations with repetition is C(n + k - 1, k) when choosing k elements from n types. Wait, actually, the formula is C(n + k - 1, k) if we have n types. But in our case, the number of types is (n + 1) (since 0 to n inclusive is n+1 numbers). Therefore, the formula is C((n + 1) + 4 - 1, 4) = C(n + 4, 4). So that's the answer.\n\nAlternatively, another way to think about it: if we have variables a ≤ b ≤ c ≤ d ≤ n, we can transform this into variables by considering the differences between consecutive terms. Let me define new variables:\n\nLet x1 = a - 0 (since a is at least 0)\n\nx2 = b - a\n\nx3 = c - b\n\nx4 = d - c\n\nx5 = n - d\n\nEach of these x_i must be non-negative integers. Then, the sum x1 + x2 + x3 + x4 + x5 = n. Because:\n\na = x1\n\nb = x1 + x2\n\nc = x1 + x2 + x3\n\nd = x1 + x2 + x3 + x4\n\nn = d + x5 = x1 + x2 + x3 + x4 + x5\n\nTherefore, we have 5 variables x1, x2, x3, x4, x5 ≥ 0, and their sum is n. The number of non-negative integer solutions to this equation is C(n + 5 - 1, 5 - 1) = C(n + 4, 4), which matches the previous result.\n\nTherefore, the number of quadruples is C(n + 4, 4). So the answer should be the combination of n + 4 choose 4, which is (n + 4)(n + 3)(n + 2)(n + 1)/24.\n\nLet me check with n=0. If n=0, the only quadruple is (0,0,0,0). The formula gives C(4,4)=1, which is correct. If n=1, as before, C(5,4)=5, which matched the earlier count. So this seems solid.\n\nTherefore, the solution is to compute the combination of n+4 choose 4, which is the formula for combinations with repetition, and that gives the number of ordered quadruples (a,b,c,d) with 0 ≤ a ≤ b ≤ c ≤ d ≤ n.\n\n**Final Answer**\nThe number of such quadruples is \\boxed{\\dbinom{n + 4}{4}}.\n</think>To determine the number of quadruples \\((a, b, c, d)\\) of non-negative integers that satisfy \\(0 \\leq a \\leq b \\leq c \\leq d \\leq n\\), we can use the concept of combinations with repetition.\n\n### Step-by-Step Solution:\n\n1. **Understanding the Problem:**\n   We need to count the number of non-decreasing sequences of length 4 where each element is between 0 and \\(n\\) (inclusive).\n\n2. **Transformation of Variables:**\n   Define new variables \\(x_1, x_2, x_3, x_4, x_5\\) such that:\n   \\[\n   x_1 = a, \\quad x_2 = b - a, \\quad x_3 = c - b, \\quad x_4 = d - c, \\quad x_5 = n - d\n   \\]\n   These variables satisfy the equation:\n   \\[\n   x_1 + x_2 + x_3 + x_4 + x_5 = n\n   \\]\n   where \\(x_1, x_2, x_3, x_4, x_5\\) are non-negative integers.\n\n3. **Counting Solutions:**\n   The number of non-negative integer solutions to the equation \\(x_1 + x_2 + x_3 + x_4 + x_5 = n\\) is given by the combination with repetition formula:\n   \\[\n   \\binom{n + 5 - 1}{5 - 1} = \\binom{n + 4}{4}\n   \\]\n\n4. **Conclusion:**\n   Therefore, the number of quadruples \\((a, b, c, d)\\) that satisfy \\(0 \\leq a \\leq b \\leq c \\leq d \\leq n\\) is:\n   \\[\n   \\boxed{\\binom{n + 4}{4}}\n   \\]\n\nThis solution leverages the combinatorial method of counting non-negative integer solutions to a linear equation, ensuring that the sequence \\(a, b, c, d\\) is non-decreasing and within the given bounds."}