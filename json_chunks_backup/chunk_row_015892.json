{"row_number": 15892, "expected_answer": "1", "problem": "Maximize \\(\\sum_{i=1}^{n} X_i Y_i\\) subject to the constraints \\(\\sum_{i=1}^{n} (X_i)^2 = 1\\) and \\(\\sum_{i=1}^{n} (Y_i)^2 = 1\\).", "generated_solution": "<think>\nOkay, let's see. I need to maximize the sum of X_i Y_i for i from 1 to n, given that the sum of X_i squared is 1 and the sum of Y_i squared is also 1. Hmm. So, the problem is to maximize the dot product of two vectors X and Y, each of which has a Euclidean norm of 1. \n\nWait, right, the dot product of two vectors is related to the cosine of the angle between them. So, the formula for the dot product is |X||Y|cos(theta), where theta is the angle between X and Y. Since both |X| and |Y| are 1 here (because their squares sum to 1), the dot product simplifies to cos(theta). The maximum value of cos(theta) is 1, which occurs when theta is 0, meaning the vectors are in the same direction. So, does that mean the maximum value of the sum is 1? But wait, let me make sure I'm not missing anything here.\n\nAlternatively, maybe I should use the <PERSON><PERSON><PERSON><PERSON> inequality. The Cauchy-<PERSON>rz inequality states that for any vectors X and Y, the absolute value of their dot product is less than or equal to the product of their norms. So, |X ⋅ Y| ≤ ||X|| ||Y||. Since both norms are 1 here, the maximum possible value of the dot product is 1. Therefore, the maximum value of the sum is indeed 1. \n\nBut wait, how do we know when equality holds in <PERSON><PERSON><PERSON><PERSON>? Equality holds if and only if the vectors are linearly dependent, meaning one is a scalar multiple of the other. Since both vectors have norm 1, the scalar must be 1 or -1. So, if X equals Y, then the dot product would be the sum of their squares, which is 1. So, yes, when X and Y are the same unit vector, their dot product is 1, which is the maximum possible. \n\nLet me check with a simple case. Let n = 1. Then X_1^2 = 1 and Y_1^2 = 1, so X_1 and Y_1 are either 1 or -1. The product X_1 Y_1 would be 1 if they are the same, or -1 otherwise. So maximum is 1. \n\nAnother case: n = 2. Let’s take X = (1, 0) and Y = (1, 0). Their dot product is 1. If I take Y = (0,1), the dot product is 0. If I take some other vector Y = (a, b) with a² + b² =1, then the dot product is a*1 + b*0 = a. The maximum value of a is 1, so again, maximum is 1. \n\nTherefore, it seems that the maximum is indeed 1, achieved when X and Y are the same unit vector. \n\nAlternatively, maybe I should use Lagrange multipliers for a more formal approach. Let me try that. \n\nWe want to maximize f(X,Y) = sum X_i Y_i subject to the constraints g(X) = sum X_i² -1 =0 and h(Y) = sum Y_i² -1 =0.\n\nThe Lagrangian would be L = sum X_i Y_i - λ (sum X_i² -1) - μ (sum Y_i² -1). Taking partial derivatives with respect to each X_i and Y_i and setting them to zero.\n\nFor each X_i: partial L / partial X_i = Y_i - 2λ X_i =0 ⇒ Y_i = 2λ X_i.\n\nFor each Y_i: partial L / partial Y_i = X_i - 2μ Y_i =0 ⇒ X_i = 2μ Y_i.\n\nSo from the first equation, Y_i = 2λ X_i. Substitute into the second equation: X_i = 2μ (2λ X_i) ⇒ X_i = 4λ μ X_i. If X_i is not zero, then 1 =4λ μ. \n\nNow, from the first equation, Y_i = 2λ X_i. Let's substitute into the second equation again. Wait, but maybe it's better to relate λ and μ. Let's see: since Y_i = 2λ X_i and X_i = 2μ Y_i, substituting the first into the second gives X_i = 2μ*(2λ X_i) ⇒ X_i = 4λ μ X_i. Therefore, if X_i ≠0, then 4λ μ =1. \n\nBut then, considering all variables, perhaps X and Y are proportional. Let’s assume that Y_i = k X_i for some scalar k. Then substituting into the constraints: sum (X_i)^2 =1, and sum (Y_i)^2 = sum (k X_i)^2 = k² sum X_i² =k²*1=1. Therefore, k²=1 ⇒ k=±1. So Y_i = X_i or Y_i = -X_i. Then the dot product sum X_i Y_i would be sum X_i^2 =1 or -1. So maximum is 1. \n\nTherefore, the maximum occurs when Y = X or Y = -X. But since we are maximizing, the maximum is 1 when Y = X. \n\nTherefore, the answer should be 1. \n\nBut let me confirm with Lagrange multipliers. Suppose we have Y_i = 2λ X_i and X_i = 2μ Y_i. Let’s plug Y_i =2λ X_i into X_i =2μ Y_i. Then X_i =2μ*(2λ X_i) ⇒ X_i =4λ μ X_i. For non-zero X_i, this implies 4λ μ =1. \n\nAlso, since Y_i =2λ X_i, substituting into the constraint sum Y_i² =1 gives sum (4λ² X_i²) =4λ² sum X_i² =4λ²*1=1 ⇒ 4λ²=1 ⇒ λ²=1/4 ⇒ λ=1/2 or -1/2.\n\nSimilarly, from X_i =2μ Y_i and Y_i=2λ X_i, we can substitute Y_i into X_i: X_i=2μ*(2λ X_i) ⇒ same equation as before. So 4λ μ=1. If λ=1/2, then 4*(1/2)*μ=1 ⇒ 2 μ=1 ⇒ μ=1/2. Similarly, if λ=-1/2, then 4*(-1/2)*μ=1 ⇒ -2 μ=1 ⇒ μ=-1/2. \n\nSo the possible solutions are λ=1/2 and μ=1/2, or λ=-1/2 and μ=-1/2. Let's take λ=1/2. Then Y_i=2*(1/2) X_i= X_i. Similarly, with λ=-1/2, Y_i= -X_i. Then the dot product sum X_i Y_i would be sum X_i^2=1 if Y_i=X_i, or sum -X_i^2=-1 if Y_i=-X_i. Hence the maximum is 1. \n\nTherefore, the maximum is indeed 1, achieved when Y=X. \n\nSo, after all these approaches—Cauchy-Schwarz, Lagrangian multipliers, specific examples—it seems the answer is 1. So I think that's correct.\n</think>To maximize the expression \\(\\sum_{i=1}^{n} X_i Y_i\\) subject to the constraints \\(\\sum_{i=1}^{n} X_i^2 = 1\\) and \\(\\sum_{i=1}^{n} Y_i^2 = 1\\), we can use the Cauchy-Schwarz inequality and the method of Lagrange multipliers.\n\n### Using Cauchy-Schwarz Inequality\nThe Cauchy-Schwarz inequality states that for any vectors \\(X\\) and \\(Y\\):\n\\[\n\\left| \\sum_{i=1}^{n} X_i Y_i \\right| \\leq \\sqrt{\\sum_{i=1}^{n} X_i^2} \\cdot \\sqrt{\\sum_{i=1}^{n} Y_i^2}.\n\\]\nGiven the constraints \\(\\sum_{i=1}^{n} X_i^2 = 1\\) and \\(\\sum_{i=1}^{n} Y_i^2 = 1\\), the inequality simplifies to:\n\\[\n\\left| \\sum_{i=1}^{n} X_i Y_i \\right| \\leq 1.\n\\]\nThus, the maximum possible value of \\(\\sum_{i=1}^{n} X_i Y_i\\) is \\(1\\).\n\nEquality in the Cauchy-Schwarz inequality holds if and only if \\(X\\) and \\(Y\\) are linearly dependent. Since both vectors have unit norm, this means \\(Y_i = k X_i\\) for some scalar \\(k\\). Given the constraints, \\(k\\) must be \\(\\pm 1\\). Therefore, \\(Y_i = X_i\\) or \\(Y_i = -X_i\\).\n\n### Using Lagrange Multipliers\nTo use Lagrange multipliers, we define the Lagrangian:\n\\[\nL = \\sum_{i=1}^{n} X_i Y_i - \\lambda \\left( \\sum_{i=1}^{n} X_i^2 - 1 \\right) - \\mu \\left( \\sum_{i=1}^{n} Y_i^2 - 1 \\right).\n\\]\nTaking partial derivatives and setting them to zero, we get:\n\\[\n\\frac{\\partial L}{\\partial X_i} = Y_i - 2\\lambda X_i = 0 \\quad \\Rightarrow \\quad Y_i = 2\\lambda X_i,\n\\]\n\\[\n\\frac{\\partial L}{\\partial Y_i} = X_i - 2\\mu Y_i = 0 \\quad \\Rightarrow \\quad X_i = 2\\mu Y_i.\n\\]\nSubstituting \\(Y_i = 2\\lambda X_i\\) into \\(X_i = 2\\mu Y_i\\), we obtain:\n\\[\nX_i = 2\\mu (2\\lambda X_i) \\quad \\Rightarrow \\quad X_i = 4\\lambda \\mu X_i.\n\\]\nFor non-zero \\(X_i\\), this implies:\n\\[\n4\\lambda \\mu = 1 \\quad \\Rightarrow \\quad \\lambda \\mu = \\frac{1}{4}.\n\\]\nUsing \\(Y_i = 2\\lambda X_i\\) in the constraint \\(\\sum_{i=1}^{n} Y_i^2 = 1\\), we get:\n\\[\n\\sum_{i=1}^{n} (2\\lambda X_i)^2 = 1 \\quad \\Rightarrow \\quad 4\\lambda^2 \\sum_{i=1}^{n} X_i^2 = 1 \\quad \\Rightarrow \\quad 4\\lambda^2 = 1 \\quad \\Rightarrow \\quad \\lambda = \\pm \\frac{1}{2}.\n\\]\nSimilarly, from \\(X_i = 2\\mu Y_i\\), we get:\n\\[\n\\sum_{i=1}^{n} (2\\mu Y_i)^2 = 1 \\quad \\Rightarrow \\quad 4\\mu^2 \\sum_{i=1}^{n} Y_i^2 = 1 \\quad \\Rightarrow \\quad 4\\mu^2 = 1 \\quad \\Rightarrow \\quad \\mu = \\pm \\frac{1}{2}.\n\\]\nThus, the possible solutions are:\n\\[\n\\lambda = \\frac{1}{2}, \\mu = \\frac{1}{2} \\quad \\text{or} \\quad \\lambda = -\\frac{1}{2}, \\mu = -\\frac{1}{2}.\n\\]\nFor \\(\\lambda = \\frac{1}{2}\\) and \\(\\mu = \\frac{1}{2}\\), we have \\(Y_i = X_i\\). For \\(\\lambda = -\\frac{1}{2}\\) and \\(\\mu = -\\frac{1}{2}\\), we have \\(Y_i = -X_i\\).\n\n### Conclusion\nThe maximum value of \\(\\sum_{i=1}^{n} X_i Y_i\\) is achieved when \\(Y_i = X_i\\), yielding:\n\\[\n\\sum_{i=1}^{n} X_i Y_i = \\sum_{i=1}^{n} X_i^2 = 1.\n\\]\nThus, the maximum value is \\(\\boxed{1}\\)."}