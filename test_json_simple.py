#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版JSON翻译测试脚本 - 专门处理LaTeX公式转义问题
"""

import json
import requests
import time
import os
import re
import logging
from typing import Dict, Any

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleVLLMTranslator:
    """简化版VLLM翻译器，专门处理JSON转义问题"""

    def __init__(self, model_name: str = "/home/<USER>/Model/LLM/Qwen/Qwen2.5-7B-Instruct-AWQ",
                 base_url: str = "http://localhost:8666"):
        self.model_name = model_name
        self.base_url = base_url
        self.api_url = f"{base_url}/v1/chat/completions"
        self.session = requests.Session()

        # 超时配置
        self.connect_timeout = 30
        self.read_timeout = 300

    def translate_json_file(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """翻译JSON文件中的指定字段"""

        # 构建更简单的翻译提示，要求模型分别翻译每个字段
        expected_answer = json_data.get("expected_answer", "")
        problem = json_data.get("problem", "")
        generated_solution = json_data.get("generated_solution", "")

        # 分别翻译每个字段
        translated_expected_answer = self._translate_field("expected_answer", expected_answer)
        translated_problem = self._translate_field("problem", problem)
        translated_solution = self._translate_field("generated_solution", generated_solution)

        if translated_expected_answer is not None and translated_problem is not None and translated_solution is not None:
            # 构建翻译后的JSON
            result = {
                "row_number": json_data.get("row_number"),
                "expected_answer": translated_expected_answer,
                "problem": translated_problem,
                "generated_solution": translated_solution
            }
            return result
        else:
            return None

    def _translate_field(self, field_name: str, content: str) -> str:
        """翻译单个字段"""
        if not content:
            return content

        # 根据字段类型调整提示词
        if field_name == "expected_answer":
            prompt = f"""请将以下内容翻译成中文，保持数字和符号不变：
{content}

只输出翻译结果，不要添加任何解释："""
        elif field_name == "problem":
            prompt = f"""请将以下数学问题翻译成中文，保持所有数学公式、符号、数字不变：
{content}

只输出翻译结果，不要添加任何解释："""
        else:  # generated_solution
            prompt = f"""请将以下数学解答翻译成中文，要求：
1. 保持所有数学公式、符号、数字不变
2. 必须保留<think></think>标签及其内容，并翻译标签内的思考过程
3. 准确翻译数学术语和解题过程
4. 保持原有的格式和结构

内容：
{content}

只输出翻译结果，不要添加任何解释："""

        payload = {
            "model": self.model_name,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.1,
            "max_tokens": 8192,
            "stream": False,
            "extra_body": {
                "chat_template_kwargs": {
                    "enable_thinking": False
                }
            }
        }

        try:
            response = self.session.post(
                self.api_url,
                json=payload,
                timeout=(self.connect_timeout, self.read_timeout)
            )
            response.raise_for_status()

            result = response.json()
            translated_text = result['choices'][0]['message']['content'].strip()

            logger.info(f"翻译字段 {field_name} 成功，长度: {len(translated_text)}")
            return translated_text

        except Exception as e:
            logger.error(f"翻译字段 {field_name} 失败: {e}")
            return None

    def check_vllm_connection(self) -> bool:
        """检查VLLM服务是否可用"""
        try:
            test_payload = {
                "model": self.model_name,
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 10,
                "temperature": 0.1
            }

            response = self.session.post(
                self.api_url,
                json=test_payload,
                timeout=(self.connect_timeout, 30)
            )
            response.raise_for_status()

            logger.info(f"VLLM服务正常，模型 {self.model_name} 可用")
            return True

        except Exception as e:
            logger.error(f"无法连接到VLLM服务: {e}")
            return False


def test_simple_translation():
    """测试简化版翻译"""
    test_file = "/home/<USER>/WorkSpace/notebooks/json_chunks/chunk_row_000001.json"

    logger.info("=" * 60)
    logger.info("开始简化版JSON翻译测试")
    logger.info("=" * 60)

    # 检查测试文件是否存在
    if not os.path.exists(test_file):
        logger.error(f"测试文件不存在: {test_file}")
        return

    # 读取测试文件
    try:
        with open(test_file, 'r', encoding='utf-8') as f:
            original_data = json.load(f)
        logger.info(f"成功读取测试文件: {test_file}")
        logger.info(f"原始数据字段: {list(original_data.keys())}")
    except Exception as e:
        logger.error(f"读取测试文件失败: {e}")
        return

    # 初始化翻译器
    translator = SimpleVLLMTranslator()

    # 检查VLLM连接
    logger.info("🔍 正在检查VLLM服务状态...")
    if not translator.check_vllm_connection():
        logger.error("❌ 无法连接到VLLM服务")
        return
    else:
        logger.info("✅ VLLM服务连接正常")

    # 显示原始数据预览
    logger.info("\n原始数据预览:")
    for field in ["expected_answer", "problem", "generated_solution"]:
        if field in original_data:
            content = str(original_data[field])
            logger.info(f"{field}: {content[:100]}...")

    # 开始翻译
    logger.info("\n开始分字段翻译...")
    start_time = time.time()

    translated_data = translator.translate_json_file(original_data)

    end_time = time.time()
    duration = end_time - start_time

    if translated_data:
        logger.info(f"✅ 翻译成功! 耗时: {duration:.2f} 秒")

        # 显示翻译结果预览
        logger.info("\n翻译结果预览:")
        for field in ["expected_answer", "problem", "generated_solution"]:
            if field in translated_data:
                content = str(translated_data[field])
                logger.info(f"{field}: {content[:200]}...")

        # 保存翻译结果到文件
        output_file = "test_simple_translation_result.json"
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(translated_data, f, ensure_ascii=False, indent=2)
            logger.info(f"翻译结果已保存到: {output_file}")

            # 验证保存的文件是否可以正确读取
            with open(output_file, 'r', encoding='utf-8') as f:
                verification_data = json.load(f)
            logger.info("✅ 保存的JSON文件格式验证通过")

        except Exception as e:
            logger.error(f"保存翻译结果失败: {e}")
    else:
        logger.error("❌ 翻译失败")

    logger.info("测试完成!")


if __name__ == "__main__":
    test_simple_translation()
