#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试<think>标签结构保持的脚本
"""

import json
import os
from json_FY_vllm import VLLMTranslator
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_think_structure():
    """测试<think>标签结构是否正确保持"""
    
    # 使用原始备份文件进行测试
    test_file = "test_json_chunks_backup/test_chunk_002.json"
    
    if not os.path.exists(test_file):
        logger.error(f"测试文件不存在: {test_file}")
        return
    
    # 读取原始文件
    with open(test_file, 'r', encoding='utf-8') as f:
        original_data = json.load(f)
    
    logger.info("原始generated_solution结构分析:")
    original_solution = original_data.get("generated_solution", "")
    
    # 分析原始结构
    think_start = original_solution.find("<think>")
    think_end = original_solution.find("</think>")
    
    if think_start != -1 and think_end != -1:
        think_content = original_solution[think_start:think_end + 8]  # 包含</think>
        after_think = original_solution[think_end + 8:].strip()
        
        logger.info(f"<think>标签开始位置: {think_start}")
        logger.info(f"</think>标签结束位置: {think_end + 8}")
        logger.info(f"<think>内容长度: {len(think_content)}")
        logger.info(f"</think>后内容长度: {len(after_think)}")
        logger.info(f"</think>后内容预览: {after_think[:100]}...")
    else:
        logger.error("未找到<think>标签")
        return
    
    # 初始化翻译器
    translator = VLLMTranslator(
        model_name="/home/<USER>/Model/LLM/Qwen/Qwen2.5-7B-Instruct-AWQ",
        base_url="http://localhost:8666",
        max_concurrent=4
    )
    
    # 检查VLLM连接
    if not translator.check_vllm_connection():
        logger.error("❌ 无法连接到VLLM服务")
        return
    
    logger.info("开始翻译generated_solution字段...")
    
    # 翻译generated_solution字段
    translated_solution = translator._translate_field("generated_solution", original_solution)
    
    if translated_solution:
        logger.info("翻译成功!")
        
        # 分析翻译后的结构
        logger.info("\n翻译后generated_solution结构分析:")
        
        trans_think_start = translated_solution.find("<think>")
        trans_think_end = translated_solution.find("</think>")
        
        if trans_think_start != -1 and trans_think_end != -1:
            trans_think_content = translated_solution[trans_think_start:trans_think_end + 8]
            trans_after_think = translated_solution[trans_think_end + 8:].strip()
            
            logger.info(f"<think>标签开始位置: {trans_think_start}")
            logger.info(f"</think>标签结束位置: {trans_think_end + 8}")
            logger.info(f"<think>内容长度: {len(trans_think_content)}")
            logger.info(f"</think>后内容长度: {len(trans_after_think)}")
            logger.info(f"</think>后内容预览: {trans_after_think[:100]}...")
            
            # 验证结构完整性
            logger.info("\n结构完整性检查:")
            
            # 检查1: 是否包含<think>和</think>标签
            has_think_tags = "<think>" in translated_solution and "</think>" in translated_solution
            logger.info(f"✅ 包含<think>和</think>标签: {has_think_tags}")
            
            # 检查2: </think>后是否有内容
            has_content_after_think = len(trans_after_think) > 0
            logger.info(f"{'✅' if has_content_after_think else '❌'} </think>后有内容: {has_content_after_think}")
            
            # 检查3: 是否包含中文翻译
            has_chinese_in_think = any('\u4e00' <= char <= '\u9fff' for char in trans_think_content)
            has_chinese_after_think = any('\u4e00' <= char <= '\u9fff' for char in trans_after_think)
            logger.info(f"✅ <think>内包含中文: {has_chinese_in_think}")
            logger.info(f"{'✅' if has_chinese_after_think else '❌'} </think>后包含中文: {has_chinese_after_think}")
            
            # 检查4: 数学公式是否保持
            has_math_formulas = "\\(" in translated_solution and "\\)" in translated_solution
            logger.info(f"✅ 保持数学公式: {has_math_formulas}")
            
            # 总体评估
            structure_correct = has_think_tags and has_content_after_think and has_chinese_after_think
            logger.info(f"\n{'✅ 结构完整性: 通过' if structure_correct else '❌ 结构完整性: 失败'}")
            
            if structure_correct:
                # 保存测试结果
                result_data = {
                    "row_number": original_data.get("row_number"),
                    "expected_answer": original_data.get("expected_answer"),
                    "problem": original_data.get("problem"),
                    "generated_solution": translated_solution
                }
                
                with open("test_think_structure_result.json", 'w', encoding='utf-8') as f:
                    json.dump(result_data, f, ensure_ascii=False, indent=2)
                
                logger.info("测试结果已保存到: test_think_structure_result.json")
            
        else:
            logger.error("❌ 翻译后缺少<think>标签")
    else:
        logger.error("❌ 翻译失败")


if __name__ == "__main__":
    test_think_structure()
