#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用VLLM的Qwen2.5-7B-Instruct-AWQ模型翻译json_chunks目录下的JSON文件
支持并发翻译、进度显示、错误处理等功能
"""

import json
import requests
import time
import os
import glob
from typing import List, Dict, Any
import logging
import shutil
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 配置tqdm进度条样式
def setup_progress_bars():
    """配置进度条的全局样式"""
    tqdm.pandas()
    import warnings
    warnings.filterwarnings("ignore", category=UserWarning, module="tqdm")

class VLLMTranslator:
    """使用VLLM API进行翻译的类，支持并发翻译"""

    def __init__(self, model_name: str = "/home/<USER>/Model/LLM/Qwen/Qwen2.5-7B-Instruct-AWQ",
                 base_url: str = "http://localhost:8666", max_concurrent: int = 8):
        self.model_name = model_name
        self.base_url = base_url
        self.api_url = f"{base_url}/v1/chat/completions"
        self.max_concurrent = max_concurrent
        self.session = requests.Session()
        self.rate_limiter = threading.Semaphore(max_concurrent)

        # 超时配置
        self.connect_timeout = 30
        self.read_timeout = 300
        self.total_timeout = 360

        # 配置session
        self.session.headers.update({
            'Connection': 'keep-alive',
            'Content-Type': 'application/json'
        })

        # 设置连接池参数
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=max_concurrent,
            pool_maxsize=max_concurrent * 2,
            max_retries=0
        )
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)

    def translate_json_file(self, json_data: Dict[str, Any], max_retries: int = 3) -> Dict[str, Any]:
        """
        翻译JSON文件中的指定字段 - 使用分字段翻译方法避免JSON解析问题

        Args:
            json_data: 原始JSON数据
            max_retries: 最大重试次数

        Returns:
            翻译后的JSON数据，如果失败返回None
        """
        try:
            # 分别翻译每个字段
            expected_answer = json_data.get("expected_answer", "")
            problem = json_data.get("problem", "")
            generated_solution = json_data.get("generated_solution", "")

            # 分别翻译每个字段
            translated_expected_answer = self._translate_field("expected_answer", expected_answer, max_retries)
            translated_problem = self._translate_field("problem", problem, max_retries)
            translated_solution = self._translate_field("generated_solution", generated_solution, max_retries)

            if translated_expected_answer is not None and translated_problem is not None and translated_solution is not None:
                # 构建翻译后的JSON
                result = {
                    "row_number": json_data.get("row_number"),
                    "expected_answer": translated_expected_answer,
                    "problem": translated_problem,
                    "generated_solution": translated_solution
                }
                return result
            else:
                logger.error("部分字段翻译失败")
                return None

        except Exception as e:
            logger.error(f"翻译过程中发生异常: {e}")
            return None

    def _translate_field(self, field_name: str, content: str, max_retries: int = 3) -> str:
        """翻译单个字段"""
        if not content:
            return content

        # 根据字段类型调整提示词
        if field_name == "expected_answer":
            prompt = f"""请将以下内容翻译成中文，保持数字和符号不变：
{content}

只输出翻译结果，不要添加任何解释："""
        elif field_name == "problem":
            prompt = f"""请将以下数学问题翻译成中文，保持所有数学公式、符号、数字不变：
{content}

只输出翻译结果，不要添加任何解释："""
        else:  # generated_solution
            prompt = f"""你是一个专业的数学翻译专家。请将以下英文数学解答完整翻译成中文。

重要说明：
这个内容有特殊结构，包含两个必须都翻译的部分：
第一部分：<think>标签内的思考过程（英文）→ 必须翻译成中文
第二部分：</think>标签后的正式解答（英文）→ 必须翻译成中文

翻译规则：
1. 数学公式、符号、数字保持原样不变
2. <think></think>标签结构必须保持
3. <think>内的英文思考过程 → 翻译成中文
4. </think>后的英文正式解答 → 翻译成中文
5. 保持所有格式、换行、缩进

示例结构：
输入：<think>English thinking...</think>English solution...
输出：<think>中文思考...</think>中文解答...

现在翻译以下内容：

{content}

要求：输出完整翻译结果，两个部分都必须是中文，保持原结构。"""

        payload = {
            "model": self.model_name,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.1,
            "max_tokens": 20480,  # 设置为20K，平衡长度和兼容性
            "stream": False,
            "extra_body": {
                "chat_template_kwargs": {
                    "enable_thinking": False
                }
            }
        }

        for attempt in range(max_retries):
            try:
                with self.rate_limiter:
                    timeout = (self.connect_timeout, self.read_timeout)

                    response = self.session.post(
                        self.api_url,
                        json=payload,
                        timeout=timeout
                    )
                    response.raise_for_status()

                    result = response.json()
                    translated_text = result['choices'][0]['message']['content'].strip()

                    # 简单验证翻译结果
                    if translated_text and len(translated_text) > 0:
                        return translated_text
                    else:
                        logger.warning(f"字段 {field_name} 翻译结果为空 (尝试 {attempt + 1})")

            except requests.exceptions.Timeout as e:
                logger.error(f"翻译字段 {field_name} 超时 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    wait_time = min(2 ** attempt, 30)
                    time.sleep(wait_time)

            except requests.exceptions.ConnectionError as e:
                logger.error(f"翻译字段 {field_name} 连接错误 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    wait_time = min(2 ** attempt, 30)
                    time.sleep(wait_time)

            except Exception as e:
                logger.error(f"翻译字段 {field_name} 错误 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    wait_time = min(2 ** attempt, 30)
                    time.sleep(wait_time)

        logger.error(f"字段 {field_name} 翻译失败")
        return None

    def _validate_translated_json(self, translated_json: Dict[str, Any], original_json: Dict[str, Any]) -> bool:
        """验证翻译后的JSON是否有效"""
        # 检查必要字段是否存在
        required_fields = ["row_number", "expected_answer", "problem", "generated_solution"]
        for field in required_fields:
            if field not in translated_json:
                logger.warning(f"翻译结果缺少字段: {field}")
                return False

        # 检查row_number是否保持不变
        if translated_json.get("row_number") != original_json.get("row_number"):
            logger.warning("row_number字段被修改")
            return False

        # 检查是否包含中文字符（简单验证翻译是否成功）
        text_fields = ["expected_answer", "problem", "generated_solution"]
        has_chinese = False
        for field in text_fields:
            if field in translated_json and translated_json[field]:
                text = str(translated_json[field])
                if any('\u4e00' <= char <= '\u9fff' for char in text):
                    has_chinese = True
                    break

        if not has_chinese:
            logger.warning("翻译结果中未检测到中文字符")
            return False

        return True

    def check_vllm_connection(self) -> bool:
        """检查VLLM服务是否可用"""
        try:
            test_payload = {
                "model": self.model_name,
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 10,
                "temperature": 0.1
            }

            timeout = (self.connect_timeout, min(self.read_timeout, 30))
            response = self.session.post(
                self.api_url,
                json=test_payload,
                timeout=timeout
            )
            response.raise_for_status()

            logger.info(f"VLLM服务正常，模型 {self.model_name} 可用")
            return True

        except Exception as e:
            logger.error(f"无法连接到VLLM服务: {e}")
            return False


class JsonChunksTranslator:
    """处理json_chunks目录下JSON文件翻译的主类"""

    def __init__(self, chunks_dir: str, translator: VLLMTranslator):
        self.chunks_dir = chunks_dir
        self.translator = translator
        self.backup_dir = f"{chunks_dir}_backup"

    def get_json_files(self) -> List[str]:
        """获取json_chunks目录下的所有JSON文件"""
        if not os.path.exists(self.chunks_dir):
            logger.error(f"目录不存在: {self.chunks_dir}")
            return []

        json_files = glob.glob(os.path.join(self.chunks_dir, "*.json"))
        json_files.sort()  # 按文件名排序

        logger.info(f"发现 {len(json_files)} 个JSON文件")
        return json_files

    def backup_files(self, json_files: List[str]):
        """备份原始文件"""
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
            logger.info(f"创建备份目录: {self.backup_dir}")

        logger.info("开始备份原始文件...")
        with tqdm(total=len(json_files), desc="📁 备份文件", unit="文件", colour='blue') as pbar:
            for file_path in json_files:
                filename = os.path.basename(file_path)
                backup_path = os.path.join(self.backup_dir, filename)

                if not os.path.exists(backup_path):
                    shutil.copy2(file_path, backup_path)

                pbar.update(1)

        logger.info("文件备份完成")

    def translate_single_file(self, file_path: str) -> bool:
        """翻译单个JSON文件"""
        try:
            # 读取原始文件
            with open(file_path, 'r', encoding='utf-8') as f:
                original_data = json.load(f)

            filename = os.path.basename(file_path)
            row_number = original_data.get('row_number', 'unknown')

            # 检查是否已经翻译过（通过检查是否包含中文字符来判断）
            text_fields = ["expected_answer", "problem", "generated_solution"]
            already_translated = True
            for field in text_fields:
                if field in original_data and original_data[field]:
                    text = str(original_data[field])
                    # 简单检查是否包含中文字符
                    if not any('\u4e00' <= char <= '\u9fff' for char in text):
                        already_translated = False
                        break

            if already_translated:
                logger.debug(f"文件 {filename} (row {row_number}) 已翻译，跳过")
                return True

            # 调用翻译API
            translated_data = self.translator.translate_json_file(original_data)

            if translated_data:
                # 覆盖写入翻译后的数据
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(translated_data, f, ensure_ascii=False, indent=2)

                return True
            else:
                logger.error(f"翻译文件 {filename} 失败：API返回空结果")
                return False

        except Exception as e:
            logger.error(f"翻译文件 {file_path} 失败: {e}")
            return False

    def translate_all_files(self, max_workers: int = 5) -> bool:
        """翻译所有JSON文件"""
        json_files = self.get_json_files()
        if not json_files:
            logger.error("没有找到JSON文件")
            return False

        # 备份原始文件
        self.backup_files(json_files)

        logger.info(f"开始翻译 {len(json_files)} 个JSON文件")
        logger.info(f"并发数: {max_workers}")

        failed_files = []
        completed_count = 0
        start_time = time.time()

        # 使用ThreadPoolExecutor进行并发翻译
        with tqdm(total=len(json_files), desc="🌍 翻译进度", unit="文件", colour='green') as pbar:
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有翻译任务
                future_to_file = {
                    executor.submit(self.translate_single_file, file_path): file_path
                    for file_path in json_files
                }

                # 收集结果
                for future in as_completed(future_to_file):
                    file_path = future_to_file[future]
                    filename = os.path.basename(file_path)

                    try:
                        success = future.result()
                        if not success:
                            failed_files.append(file_path)
                    except Exception as e:
                        logger.error(f"处理文件 {filename} 时发生异常: {e}")
                        failed_files.append(file_path)

                    completed_count += 1
                    pbar.update(1)

                    # 计算并显示统计信息
                    elapsed_time = time.time() - start_time
                    if completed_count > 0:
                        speed = completed_count / elapsed_time
                        remaining_tasks = len(json_files) - completed_count
                        eta_seconds = remaining_tasks / speed if speed > 0 else 0
                        eta_minutes = eta_seconds / 60

                        pbar.set_postfix({
                            "速度": f"{speed:.1f}文件/秒",
                            "剩余": f"{eta_minutes:.1f}分钟",
                            "失败": len(failed_files),
                            "成功率": f"{((completed_count - len(failed_files)) / completed_count * 100):.1f}%"
                        })

        # 处理失败的文件
        if failed_files:
            logger.warning(f"有 {len(failed_files)} 个文件翻译失败")
            for file_path in failed_files:
                logger.warning(f"失败文件: {os.path.basename(file_path)}")

            user_choice = input("是否重试失败的文件？(y/n): ")
            if user_choice.lower() == 'y':
                logger.info("重试失败的文件...")
                retry_failed = []

                with tqdm(total=len(failed_files), desc="🔄 重试失败文件", unit="文件", colour='red') as retry_pbar:
                    with ThreadPoolExecutor(max_workers=max_workers) as retry_executor:
                        retry_future_to_file = {
                            retry_executor.submit(self.translate_single_file, file_path): file_path
                            for file_path in failed_files
                        }

                        for future in as_completed(retry_future_to_file):
                            file_path = retry_future_to_file[future]
                            try:
                                success = future.result()
                                if not success:
                                    retry_failed.append(file_path)
                            except Exception as e:
                                logger.error(f"重试文件 {file_path} 时发生异常: {e}")
                                retry_failed.append(file_path)

                            retry_pbar.update(1)

                if retry_failed:
                    logger.error(f"仍有 {len(retry_failed)} 个文件翻译失败")
                    failed_files = retry_failed
                else:
                    logger.info("所有文件重试成功")
                    failed_files = []

        # 显示最终结果
        total_files = len(json_files)
        success_files = total_files - len(failed_files)
        success_rate = (success_files / total_files * 100) if total_files > 0 else 0

        logger.info(f"\n翻译任务完成!")
        logger.info(f"总文件数: {total_files}")
        logger.info(f"成功翻译: {success_files}")
        logger.info(f"失败文件: {len(failed_files)}")
        logger.info(f"成功率: {success_rate:.1f}%")
        logger.info(f"总耗时: {(time.time() - start_time):.2f} 秒")

        return len(failed_files) == 0


def main():
    """主函数"""
    # 配置进度条
    setup_progress_bars()

    # 配置参数
    chunks_dir = "/home/<USER>/WorkSpace/notebooks/json_chunks"
    model_name = "/home/<USER>/Model/LLM/Qwen/Qwen2.5-7B-Instruct-AWQ"
    base_url = "http://localhost:8666"
    max_concurrent = 8  # VLLM内部并发限制
    max_workers = 5     # 文件处理并发数

    logger.info("开始JSON文件翻译任务")
    logger.info("=" * 60)

    print(f"翻译配置:")
    print(f"- 模型: {model_name}")
    print(f"- 服务地址: {base_url}")
    print(f"- JSON目录: {chunks_dir}")
    print(f"- VLLM并发限制: {max_concurrent}")
    print(f"- 文件处理并发: {max_workers}")
    print(f"- 翻译字段: expected_answer, problem, generated_solution")

    # 初始化翻译器
    translator = VLLMTranslator(
        model_name=model_name,
        base_url=base_url,
        max_concurrent=max_concurrent
    )

    # 检查VLLM连接
    logger.info("🔍 正在检查VLLM服务状态...")
    if not translator.check_vllm_connection():
        logger.error("❌ 无法连接到VLLM服务")
        logger.error("\n🛠️ 请检查以下项目:")
        logger.error("1. VLLM服务是否正在运行在8666端口")
        logger.error("2. Qwen2.5-7B-Instruct-AWQ模型是否已正确加载")
        logger.error("3. 服务地址是否正确 (http://localhost:8666)")
        logger.error("4. 服务器资源是否充足 (GPU/CPU/内存)")
        logger.error("5. 是否有其他进程占用端口")
        return
    else:
        logger.info("✅ VLLM服务连接正常")

    # 初始化JSON文件处理器
    processor = JsonChunksTranslator(chunks_dir, translator)

    # 获取文件列表
    json_files = processor.get_json_files()
    if not json_files:
        logger.error("没有找到JSON文件，请检查目录路径")
        return

    # 显示任务概览
    print(f"\n翻译任务概览:")
    print(f"- 总文件数: {len(json_files)}")
    print(f"- 翻译字段: expected_answer, problem, generated_solution")
    print(f"- 文件并发数: {max_workers}")
    print(f"- 支持断点续存: 是（自动跳过已翻译文件）")
    print(f"- 自动备份: 是（备份到 {processor.backup_dir}）")

    # 询问用户确认
    user_input = input(f"\n是否开始翻译？(y/n): ")
    if user_input.lower() != 'y':
        logger.info("用户取消翻译任务")
        return

    # 开始翻译
    start_time = time.time()
    success = processor.translate_all_files(max_workers=max_workers)

    if success:
        # 显示统计信息
        end_time = time.time()
        duration = end_time - start_time

        logger.info(f"\n翻译完成!")
        logger.info(f"总耗时: {duration:.2f} 秒 ({duration/60:.2f} 分钟)")
        logger.info(f"处理文件数: {len(json_files)}")
        logger.info(f"平均速度: {len(json_files)/duration:.2f} 文件/秒")

        # 显示翻译结果预览
        try:
            sample_file = json_files[0]
            with open(sample_file, 'r', encoding='utf-8') as f:
                sample_data = json.load(f)

            logger.info("\n翻译结果预览:")
            for field in ["expected_answer", "problem", "generated_solution"]:
                if field in sample_data:
                    translated = sample_data[field]
                    logger.info(f"\n{field} (翻译后):")
                    logger.info(f"{str(translated)[:200]}...")
        except Exception as e:
            logger.error(f"读取预览结果失败: {e}")
    else:
        logger.error("翻译任务未完全成功")

    logger.info("任务完成!")


if __name__ == "__main__":
    main()