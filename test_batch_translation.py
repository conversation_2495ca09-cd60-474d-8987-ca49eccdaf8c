#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量翻译测试脚本 - 测试修复后的json_FY_vllm.py的核心功能
"""

import json
import os
import glob
import shutil
from json_FY_vllm import VLLMTranslator, JsonChunksTranslator
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_batch_translation():
    """测试批量翻译功能"""
    
    # 创建测试目录
    test_chunks_dir = "test_json_chunks"
    if os.path.exists(test_chunks_dir):
        shutil.rmtree(test_chunks_dir)
    os.makedirs(test_chunks_dir)
    
    # 复制前5个文件到测试目录
    source_files = glob.glob("json_chunks/chunk_row_*.json")[:5]
    test_files = []
    
    for i, source_file in enumerate(source_files):
        test_file = os.path.join(test_chunks_dir, f"test_chunk_{i+1:03d}.json")
        shutil.copy2(source_file, test_file)
        test_files.append(test_file)
        logger.info(f"复制测试文件: {os.path.basename(source_file)} -> {os.path.basename(test_file)}")
    
    logger.info(f"创建了 {len(test_files)} 个测试文件")
    
    # 初始化翻译器
    translator = VLLMTranslator(
        model_name="/home/<USER>/Model/LLM/Qwen/Qwen2.5-7B-Instruct-AWQ",
        base_url="http://localhost:8666",
        max_concurrent=4  # 降低并发数用于测试
    )
    
    # 检查VLLM连接
    logger.info("🔍 正在检查VLLM服务状态...")
    if not translator.check_vllm_connection():
        logger.error("❌ 无法连接到VLLM服务")
        return
    else:
        logger.info("✅ VLLM服务连接正常")
    
    # 初始化JSON文件处理器
    processor = JsonChunksTranslator(test_chunks_dir, translator)
    
    # 显示原始文件内容预览
    logger.info("\n原始文件内容预览:")
    for test_file in test_files[:2]:  # 只显示前2个文件
        try:
            with open(test_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info(f"\n文件: {os.path.basename(test_file)}")
            logger.info(f"  row_number: {data.get('row_number')}")
            logger.info(f"  expected_answer: {data.get('expected_answer')}")
            logger.info(f"  problem: {str(data.get('problem', ''))[:100]}...")
            logger.info(f"  generated_solution: {str(data.get('generated_solution', ''))[:100]}...")
        except Exception as e:
            logger.error(f"读取文件 {test_file} 失败: {e}")
    
    # 开始翻译
    logger.info(f"\n开始翻译 {len(test_files)} 个测试文件...")
    success = processor.translate_all_files(max_workers=2)  # 降低并发数
    
    if success:
        logger.info("✅ 批量翻译测试成功!")
        
        # 显示翻译结果预览
        logger.info("\n翻译结果预览:")
        for test_file in test_files[:2]:  # 只显示前2个文件
            try:
                with open(test_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                logger.info(f"\n文件: {os.path.basename(test_file)}")
                logger.info(f"  row_number: {data.get('row_number')}")
                logger.info(f"  expected_answer: {data.get('expected_answer')}")
                logger.info(f"  problem: {str(data.get('problem', ''))[:100]}...")
                logger.info(f"  generated_solution: {str(data.get('generated_solution', ''))[:100]}...")
                
                # 验证是否包含中文
                text_fields = ["expected_answer", "problem", "generated_solution"]
                has_chinese = False
                for field in text_fields:
                    if field in data and data[field]:
                        text = str(data[field])
                        if any('\u4e00' <= char <= '\u9fff' for char in text):
                            has_chinese = True
                            break
                
                if has_chinese:
                    logger.info(f"  ✅ 包含中文翻译")
                else:
                    logger.info(f"  ❌ 未检测到中文翻译")
                    
            except Exception as e:
                logger.error(f"读取翻译结果 {test_file} 失败: {e}")
    else:
        logger.error("❌ 批量翻译测试失败")
    
    # 清理测试文件
    user_choice = input("\n是否保留测试文件？(y/n): ")
    if user_choice.lower() != 'y':
        shutil.rmtree(test_chunks_dir)
        if os.path.exists(f"{test_chunks_dir}_backup"):
            shutil.rmtree(f"{test_chunks_dir}_backup")
        logger.info("测试文件已清理")
    else:
        logger.info(f"测试文件保留在: {test_chunks_dir}")
    
    logger.info("批量翻译测试完成!")


if __name__ == "__main__":
    test_batch_translation()
