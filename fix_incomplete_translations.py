#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复不完整翻译的脚本 - 专门处理缺失</think>标签的文件
"""

import json
import os
import re
import logging
from json_FY_vllm import VLLMTranslator

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_incomplete_think_tags(file_path: str) -> bool:
    """检查文件是否有不完整的<think>标签"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        generated_solution = data.get('generated_solution', '')
        if not generated_solution:
            return False
        
        # 检查是否有<think>但没有</think>
        think_start_count = generated_solution.count('<think>')
        think_end_count = generated_solution.count('</think>')
        
        if think_start_count > 0 and think_end_count < think_start_count:
            logger.info(f"发现不完整的<think>标签: {os.path.basename(file_path)}")
            logger.info(f"  <think>标签数量: {think_start_count}")
            logger.info(f"  </think>标签数量: {think_end_count}")
            return True
        
        return False
    except Exception as e:
        logger.error(f"检查文件 {file_path} 失败: {e}")
        return False

def find_incomplete_files(directory: str) -> list:
    """查找所有有不完整<think>标签的文件"""
    incomplete_files = []
    
    if not os.path.exists(directory):
        logger.error(f"目录不存在: {directory}")
        return incomplete_files
    
    json_files = [f for f in os.listdir(directory) if f.endswith('.json')]
    logger.info(f"检查 {len(json_files)} 个JSON文件...")
    
    for filename in json_files:
        file_path = os.path.join(directory, filename)
        if check_incomplete_think_tags(file_path):
            incomplete_files.append(file_path)
    
    return incomplete_files

def fix_single_file(file_path: str, translator: VLLMTranslator) -> bool:
    """修复单个文件的翻译"""
    try:
        # 从备份恢复原始文件
        backup_dir = os.path.dirname(file_path).replace('test_json_chunks', 'test_json_chunks_backup')
        backup_file = os.path.join(backup_dir, os.path.basename(file_path))
        
        if os.path.exists(backup_file):
            logger.info(f"从备份恢复原始文件: {os.path.basename(file_path)}")
            with open(backup_file, 'r', encoding='utf-8') as f:
                original_data = json.load(f)
        else:
            logger.warning(f"未找到备份文件，使用当前文件: {os.path.basename(file_path)}")
            with open(file_path, 'r', encoding='utf-8') as f:
                original_data = json.load(f)
        
        # 重新翻译
        logger.info(f"重新翻译文件: {os.path.basename(file_path)}")
        translated_data = translator.translate_json_file(original_data)
        
        if translated_data:
            # 验证翻译结果
            generated_solution = translated_data.get('generated_solution', '')
            think_start_count = generated_solution.count('<think>')
            think_end_count = generated_solution.count('</think>')
            
            if think_start_count > 0 and think_end_count >= think_start_count:
                logger.info(f"翻译成功，<think>标签完整: {think_start_count}个开始标签，{think_end_count}个结束标签")
                
                # 保存修复后的文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(translated_data, f, ensure_ascii=False, indent=2)
                
                return True
            else:
                logger.warning(f"翻译后仍然不完整: {think_start_count}个<think>，{think_end_count}个</think>")
                return False
        else:
            logger.error(f"翻译失败: {os.path.basename(file_path)}")
            return False
    
    except Exception as e:
        logger.error(f"修复文件 {file_path} 失败: {e}")
        return False

def main():
    """主函数"""
    # 配置
    test_chunks_dir = "test_json_chunks"
    model_name = "/home/<USER>/Model/LLM/Qwen/Qwen2.5-7B-Instruct-AWQ"
    base_url = "http://localhost:8666"
    
    logger.info("开始检查和修复不完整的翻译...")
    logger.info("=" * 60)
    
    # 查找有问题的文件
    incomplete_files = find_incomplete_files(test_chunks_dir)
    
    if not incomplete_files:
        logger.info("✅ 没有发现不完整的翻译文件")
        return
    
    logger.info(f"发现 {len(incomplete_files)} 个需要修复的文件:")
    for file_path in incomplete_files:
        logger.info(f"  - {os.path.basename(file_path)}")
    
    # 初始化翻译器
    translator = VLLMTranslator(
        model_name=model_name,
        base_url=base_url,
        max_concurrent=4
    )
    
    # 检查VLLM连接
    logger.info("🔍 检查VLLM服务状态...")
    if not translator.check_vllm_connection():
        logger.error("❌ 无法连接到VLLM服务")
        return
    else:
        logger.info("✅ VLLM服务连接正常")
    
    # 询问用户确认
    user_input = input(f"\n是否修复这 {len(incomplete_files)} 个文件？(y/n): ")
    if user_input.lower() != 'y':
        logger.info("用户取消修复任务")
        return
    
    # 修复文件
    success_count = 0
    for file_path in incomplete_files:
        logger.info(f"\n修复文件: {os.path.basename(file_path)}")
        if fix_single_file(file_path, translator):
            success_count += 1
            logger.info(f"✅ 修复成功")
        else:
            logger.error(f"❌ 修复失败")
    
    # 显示结果
    logger.info(f"\n修复完成!")
    logger.info(f"总文件数: {len(incomplete_files)}")
    logger.info(f"修复成功: {success_count}")
    logger.info(f"修复失败: {len(incomplete_files) - success_count}")
    logger.info(f"成功率: {(success_count / len(incomplete_files) * 100):.1f}%")

if __name__ == "__main__":
    main()
